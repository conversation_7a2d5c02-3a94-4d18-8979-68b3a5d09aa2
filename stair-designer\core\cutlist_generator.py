"""
Cut List Generator Module
Generates CSV cut lists for stair manufacturing projects
"""

import os
import csv
import json
from typing import Dict, List, Any


class CutlistGenerator:
    """Class for generating cut lists for stair manufacturing"""

    def __init__(self):
        pass

    def generate_stair_cutlist(self, stair_results: Dict, cut_list: List[Dict], output_path: str):
        """Generate cut list CSV for stair project"""
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Write CSV file
        self._write_stair_csv(stair_results, cut_list, output_path)

    def _write_stair_csv(self, stair_results: Dict, cut_list: List[Dict], output_path: str):
        """Write stair cut list to CSV file"""
        parameters = stair_results.get('parameters', {})
        rise_run = stair_results.get('rise_run', {})

        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # Header with job information
            writer.writerow(['STAIR MANUFACTURING CUT LIST'])
            writer.writerow(['=' * 50])
            writer.writerow([])

            # Job details
            writer.writerow(['JOB INFORMATION:'])
            writer.writerow(['Job Name:', parameters.get('job_name', 'Untitled')])
            writer.writerow(['Customer:', parameters.get('customer', '')])
            writer.writerow(['Date:', parameters.get('date', '')])
            writer.writerow(['Material:', stair_results.get('material', 'LSL')])
            writer.writerow([])

            # Stair specifications
            writer.writerow(['STAIR SPECIFICATIONS:'])
            writer.writerow(['Total Rise:', f"{rise_run.get('total_rise', 0):.2f}\""])
            writer.writerow(['Total Run:', f"{rise_run.get('total_run', 0):.2f}\""])
            writer.writerow(['Number of Steps:', rise_run.get('num_treads', 0)])
            writer.writerow(['Riser Height:', f"{rise_run.get('riser_height', 0):.3f}\""])
            writer.writerow(['Tread Depth:', f"{rise_run.get('tread_depth', 0):.3f}\""])
            writer.writerow(['Stair Width:', f"{parameters.get('stair_width', 36)}\""])
            writer.writerow(['Tread Thickness:', parameters.get('tread_thickness', '1\"')])
            writer.writerow(['Nosing:', 'Yes' if parameters.get('has_nosing') else 'No'])
            writer.writerow([])

            # Cut list table
            writer.writerow(['CUT LIST:'])
            writer.writerow(['Item', 'Description', 'Material', 'Dimensions (inches)', 'Quantity', 'Notes'])
            writer.writerow(['-' * 10, '-' * 20, '-' * 15, '-' * 20, '-' * 8, '-' * 20])

            # Cut list items
            for item in cut_list:
                writer.writerow([
                    item.get('item', ''),
                    item.get('description', ''),
                    item.get('material', ''),
                    item.get('dimensions', ''),
                    item.get('quantity', 1),
                    item.get('notes', '')
                ])

            writer.writerow([])

            # Summary
            writer.writerow(['SUMMARY:'])
            total_items = len(cut_list)
            total_pieces = sum(item.get('quantity', 1) for item in cut_list)

            writer.writerow(['Total Line Items:', total_items])
            writer.writerow(['Total Pieces:', total_pieces])
            writer.writerow([])

            # Code compliance
            riser_height = rise_run.get('riser_height', 0)
            tread_depth = rise_run.get('tread_depth', 0)
            two_r_plus_t = 2 * riser_height + tread_depth

            writer.writerow(['CODE COMPLIANCE CHECK:'])
            writer.writerow(['2R + T Rule:', f"{two_r_plus_t:.2f}\" (should be 24-25\")"])
            writer.writerow(['Compliant:', 'YES' if 24 <= two_r_plus_t <= 25 else 'CHECK REQUIRED'])
            writer.writerow(['Riser Height Range:', f"{riser_height:.3f}\" (7\"-7.75\" required)"])
            writer.writerow(['Tread Depth Range:', f"{tread_depth:.3f}\" (10\"-11\" required)"])
            writer.writerow([])

            # Notes
            writer.writerow(['NOTES:'])
            writer.writerow(['- All dimensions in inches unless noted'])
            writer.writerow(['- Verify all measurements before cutting'])
            writer.writerow(['- Check local building codes'])
            writer.writerow(['- Material grade: Construction or better'])

    def _get_current_date(self) -> str:
        """Get current date as string"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d")
        
    def generate(self, parameters: Dict, output_path: str):
        """Generate cut list CSV based on parameters"""
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Get template name from parameters
        template_name = parameters.get('template', 'straight')
        
        # Get template definition
        template = self.layout_engine.get_template(template_name)
        if not template:
            raise ValueError(f"Template '{template_name}' not found")
            
        # Generate cut list from template
        cut_list_template = template.get('cut_list', [])
        cut_list = self._process_cut_list(cut_list_template, parameters)
        
        # Write CSV file
        self._write_csv(cut_list, output_path, parameters)
        
    def _process_cut_list(self, cut_list_template: List[Dict], parameters: Dict) -> List[Dict]:
        """Process cut list template with parameters"""
        processed_cut_list = []
        
        for item_template in cut_list_template:
            item = {}
            
            # Process each field in the cut list item
            for field, value in item_template.items():
                if isinstance(value, str) and '{{' in value and '}}' in value:
                    # Evaluate expression
                    item[field] = self.layout_engine.evaluate_expression(value, parameters)
                else:
                    item[field] = value
                    
            # Round numeric values appropriately
            if 'quantity' in item:
                item['quantity'] = int(round(item['quantity']))
            if 'width' in item:
                item['width'] = round(item['width'], 1)
            if 'length' in item:
                item['length'] = round(item['length'], 1)
            if 'thickness' in item:
                item['thickness'] = round(item['thickness'], 1)
            if 'area' in item:
                item['area'] = round(item['area'], 2)
                
            processed_cut_list.append(item)
            
        return processed_cut_list
        
    def _write_csv(self, cut_list: List[Dict], output_path: str, parameters: Dict):
        """Write cut list to CSV file"""
        if not cut_list:
            return
            
        # Define CSV columns
        columns = [
            'item_no',
            'name',
            'material',
            'thickness',
            'width',
            'length',
            'area',
            'quantity',
            'description',
            'notes'
        ]
        
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header with job information
            writer.writerow(['AutoCAD Desktop - Cut List'])
            writer.writerow(['Job Name:', parameters.get('job_name', 'myjob')])
            writer.writerow(['Template:', parameters.get('template', 'straight')])
            writer.writerow(['Date:', self._get_current_date()])
            writer.writerow([])  # Empty row
            
            # Write parameters section
            writer.writerow(['Parameters:'])
            for key, value in parameters.items():
                if key not in ['template', 'job_name']:
                    writer.writerow([key, value])
            writer.writerow([])  # Empty row
            
            # Write cut list header
            writer.writerow(['Cut List:'])
            header_row = []
            for col in columns:
                header_row.append(col.replace('_', ' ').title())
            writer.writerow(header_row)
            
            # Write cut list items
            for i, item in enumerate(cut_list, 1):
                row = []
                row.append(i)  # item_no
                row.append(item.get('name', ''))
                row.append(item.get('material', ''))
                row.append(item.get('thickness', ''))
                row.append(item.get('width', ''))
                row.append(item.get('length', ''))
                row.append(item.get('area', ''))
                row.append(item.get('quantity', ''))
                row.append(item.get('description', ''))
                row.append(item.get('notes', ''))
                writer.writerow(row)
                
            # Write summary
            writer.writerow([])  # Empty row
            writer.writerow(['Summary:'])
            
            # Calculate totals
            total_items = len(cut_list)
            total_quantity = sum(item.get('quantity', 0) for item in cut_list)
            total_area = sum(item.get('area', 0) * item.get('quantity', 1) for item in cut_list if 'area' in item)
            
            writer.writerow(['Total Items:', total_items])
            writer.writerow(['Total Quantity:', total_quantity])
            if total_area > 0:
                writer.writerow(['Total Area (sq mm):', f"{total_area:.2f}"])
                writer.writerow(['Total Area (sq m):', f"{total_area/1000000:.4f}"])
                
            # Material summary
            writer.writerow([])
            writer.writerow(['Material Summary:'])
            material_summary = self._calculate_material_summary(cut_list)
            for material, summary in material_summary.items():
                writer.writerow([f"{material}:", f"Qty: {summary['quantity']}, Area: {summary['area']:.2f} sq mm"])
                
    def _calculate_material_summary(self, cut_list: List[Dict]) -> Dict[str, Dict]:
        """Calculate summary by material type"""
        summary = {}
        
        for item in cut_list:
            material = item.get('material', 'Unknown')
            quantity = item.get('quantity', 1)
            area = item.get('area', 0)
            
            if material not in summary:
                summary[material] = {'quantity': 0, 'area': 0}
                
            summary[material]['quantity'] += quantity
            summary[material]['area'] += area * quantity
            
        return summary
        
    def generate_detailed_cutlist(self, parameters: Dict, output_path: str):
        """Generate a more detailed cut list with additional calculations"""
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Get template name from parameters
        template_name = parameters.get('template', 'straight')
        
        # Generate geometry for additional calculations
        geometry = self.layout_engine.generate_geometry(template_name, parameters)
        
        # Get template definition
        template = self.layout_engine.get_template(template_name)
        if not template:
            raise ValueError(f"Template '{template_name}' not found")
            
        # Generate cut list from template
        cut_list_template = template.get('cut_list', [])
        cut_list = self._process_cut_list(cut_list_template, parameters)
        
        # Add additional calculations
        for item in cut_list:
            # Calculate volume if thickness is available
            if all(key in item for key in ['width', 'length', 'thickness']):
                volume = item['width'] * item['length'] * item['thickness']
                item['volume_mm3'] = round(volume, 2)
                item['volume_cm3'] = round(volume / 1000, 2)
                
            # Calculate weight estimate (assuming wood density ~600 kg/m³)
            if 'volume_mm3' in item:
                density = 0.0006  # kg/cm³ for wood
                weight = item['volume_cm3'] * density * item.get('quantity', 1)
                item['weight_kg'] = round(weight, 3)
                
            # Add cutting time estimate (rough estimate)
            if 'area' in item:
                # Assume 1 minute per 10,000 sq mm
                cutting_time = (item['area'] * item.get('quantity', 1)) / 10000
                item['cutting_time_min'] = round(cutting_time, 1)
                
        # Write detailed CSV
        self._write_detailed_csv(cut_list, output_path, parameters, geometry)
        
    def _write_detailed_csv(self, cut_list: List[Dict], output_path: str, parameters: Dict, geometry: Dict):
        """Write detailed cut list to CSV file"""
        if not cut_list:
            return
            
        # Define detailed CSV columns
        columns = [
            'item_no', 'name', 'material', 'thickness', 'width', 'length',
            'area', 'volume_mm3', 'volume_cm3', 'quantity', 'weight_kg',
            'cutting_time_min', 'description', 'notes'
        ]
        
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header with job information
            writer.writerow(['AutoCAD Desktop - Detailed Cut List'])
            writer.writerow(['Job Name:', parameters.get('job_name', 'myjob')])
            writer.writerow(['Template:', parameters.get('template', 'straight')])
            writer.writerow(['Date:', self._get_current_date()])
            writer.writerow([])
            
            # Write geometry summary
            writer.writerow(['Geometry Summary:'])
            for element_name, element in geometry.items():
                element_type = element.get('type', 'unknown')
                writer.writerow([f"{element_name}:", element_type])
                if element_type == 'array':
                    writer.writerow(['  Count:', element.get('count', 0)])
            writer.writerow([])
            
            # Write detailed cut list
            writer.writerow(['Detailed Cut List:'])
            header_row = [col.replace('_', ' ').title() for col in columns]
            writer.writerow(header_row)
            
            for i, item in enumerate(cut_list, 1):
                row = [item.get(col, '') if col != 'item_no' else i for col in columns]
                writer.writerow(row)
                
            # Write detailed summary
            writer.writerow([])
            writer.writerow(['Detailed Summary:'])
            
            total_weight = sum(item.get('weight_kg', 0) for item in cut_list)
            total_cutting_time = sum(item.get('cutting_time_min', 0) for item in cut_list)
            total_volume = sum(item.get('volume_cm3', 0) * item.get('quantity', 1) for item in cut_list)
            
            writer.writerow(['Total Weight (kg):', f"{total_weight:.3f}"])
            writer.writerow(['Total Cutting Time (min):', f"{total_cutting_time:.1f}"])
            writer.writerow(['Total Cutting Time (hours):', f"{total_cutting_time/60:.2f}"])
            writer.writerow(['Total Volume (cm³):', f"{total_volume:.2f}"])
            
    def _get_current_date(self) -> str:
        """Get current date as string"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
    def export_to_excel(self, parameters: Dict, output_path: str):
        """Export cut list to Excel format (if openpyxl is available)"""
        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment, Border, Side
        except ImportError:
            # Fallback to CSV
            csv_path = output_path.replace('.xlsx', '.csv')
            self.generate(parameters, csv_path)
            return
            
        # Generate cut list data
        template_name = parameters.get('template', 'straight')
        template = self.layout_engine.get_template(template_name)
        cut_list_template = template.get('cut_list', [])
        cut_list = self._process_cut_list(cut_list_template, parameters)
        
        # Create workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Cut List"
        
        # Add header information
        ws['A1'] = "AutoCAD Desktop - Cut List"
        ws['A1'].font = Font(bold=True, size=14)
        
        ws['A3'] = "Job Name:"
        ws['B3'] = parameters.get('job_name', 'myjob')
        ws['A4'] = "Template:"
        ws['B4'] = parameters.get('template', 'straight')
        ws['A5'] = "Date:"
        ws['B5'] = self._get_current_date()
        
        # Add cut list table
        start_row = 8
        headers = ['Item No', 'Name', 'Material', 'Thickness', 'Width', 'Length', 'Area', 'Quantity', 'Description']
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=start_row, column=col, value=header)
            cell.font = Font(bold=True)
            
        # Add cut list data
        for row, item in enumerate(cut_list, start_row + 1):
            ws.cell(row=row, column=1, value=row - start_row)
            ws.cell(row=row, column=2, value=item.get('name', ''))
            ws.cell(row=row, column=3, value=item.get('material', ''))
            ws.cell(row=row, column=4, value=item.get('thickness', ''))
            ws.cell(row=row, column=5, value=item.get('width', ''))
            ws.cell(row=row, column=6, value=item.get('length', ''))
            ws.cell(row=row, column=7, value=item.get('area', ''))
            ws.cell(row=row, column=8, value=item.get('quantity', ''))
            ws.cell(row=row, column=9, value=item.get('description', ''))
            
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
            
        # Save workbook
        wb.save(output_path)
