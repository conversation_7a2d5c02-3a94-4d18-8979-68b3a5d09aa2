#!/usr/bin/env python3
"""
Stair Manufacturing Desktop Application
Main PySide6 GUI application for automated stair design and CNC prep workflow
"""

import sys
import os
import json
from datetime import datetime
from typing import Dict, List
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QLineEdit, QPushButton,
                             QComboBox, QTextEdit, QFileDialog, QMessageBox,
                             QTabWidget, QFormLayout, QSpinBox, QDoubleSpinBox,
                             QCheckBox, QGroupBox, QGridLayout, QSplitter,
                             QTableWidget, QTableWidgetItem, QHeaderView)
from PySide6.QtCore import Qt, QThread, Signal, QSettings
from PySide6.QtGui import QPixmap, QIcon, QFont, QAction

from core.stair_geometry import StairGeometry
from core.dxf_writer import DXFWriter
from core.gcode_writer import GCodeWriter
from core.pdf_generator import PDFGenerator
from core.cutlist_generator import CutlistGenerator
from core.cost_estimator import CostEstimator


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Stair Manufacturing - Design & CNC Prep Tool")
        self.setGeometry(100, 100, 1400, 900)

        # Initialize core components
        self.stair_geometry = StairGeometry()
        self.dxf_writer = DXFWriter()
        self.gcode_writer = GCodeWriter()
        self.pdf_generator = PDFGenerator()
        self.cutlist_generator = CutlistGenerator()
        self.cost_estimator = CostEstimator()

        # Settings for save/load functionality
        self.settings = QSettings("StairMfg", "StairDesigner")

        # Current job data
        self.current_job = {}

        self.init_ui()
        self.load_defaults()
        
    def init_ui(self):
        """Initialize the user interface"""
        # Create menu bar
        self.create_menu_bar()

        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)

        # Logo/Header
        self.add_header(main_layout)

        # Create splitter for main content
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # Left panel - Input parameters
        left_panel = self.create_input_panel()
        splitter.addWidget(left_panel)

        # Right panel - Results and preview
        right_panel = self.create_results_panel()
        splitter.addWidget(right_panel)

        # Set splitter proportions
        splitter.setSizes([500, 900])

        # Status bar
        self.statusBar().showMessage("Ready - Load defaults: LSL, 1\" treads, nosing ON")

    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu('File')

        new_action = QAction('New Job', self)
        new_action.setShortcut('Ctrl+N')
        new_action.triggered.connect(self.new_job)
        file_menu.addAction(new_action)

        open_action = QAction('Open Job...', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_job)
        file_menu.addAction(open_action)

        save_action = QAction('Save Job', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_job)
        file_menu.addAction(save_action)

        save_as_action = QAction('Save Job As...', self)
        save_as_action.setShortcut('Ctrl+Shift+S')
        save_as_action.triggered.connect(self.save_job_as)
        file_menu.addAction(save_as_action)

        file_menu.addSeparator()

        duplicate_action = QAction('Duplicate Job', self)
        duplicate_action.setShortcut('Ctrl+D')
        duplicate_action.triggered.connect(self.duplicate_job)
        file_menu.addAction(duplicate_action)

        file_menu.addSeparator()

        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Tools menu
        tools_menu = menubar.addMenu('Tools')

        calc_action = QAction('Recalculate', self)
        calc_action.setShortcut('F5')
        calc_action.triggered.connect(self.calculate_stair)
        tools_menu.addAction(calc_action)

        # Help menu
        help_menu = menubar.addMenu('Help')

        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_input_panel(self):
        """Create the left input panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Job Information Group
        job_group = QGroupBox("Job Information")
        job_layout = QFormLayout(job_group)

        self.job_name_edit = QLineEdit("Job_001")
        self.customer_edit = QLineEdit()
        self.date_edit = QLineEdit(datetime.now().strftime("%Y-%m-%d"))

        job_layout.addRow("Job Name:", self.job_name_edit)
        job_layout.addRow("Customer:", self.customer_edit)
        job_layout.addRow("Date:", self.date_edit)

        layout.addWidget(job_group)

        # Stair Parameters Group
        stair_group = QGroupBox("Stair Parameters")
        stair_layout = QFormLayout(stair_group)

        # Total rise (inches and fractions)
        self.total_rise_edit = QLineEdit("108")  # Default 9 feet
        stair_layout.addRow("Total Rise (inches):", self.total_rise_edit)

        # Stair width
        self.stair_width_edit = QLineEdit("36")  # Default 36"
        stair_layout.addRow("Stair Width (inches):", self.stair_width_edit)

        # Tread thickness
        self.tread_thickness_combo = QComboBox()
        self.tread_thickness_combo.addItems(["1\"", "1-1/4\"", "1-1/2\"", "2\""])
        self.tread_thickness_combo.setCurrentText("1\"")  # Default
        stair_layout.addRow("Tread Thickness:", self.tread_thickness_combo)

        # Riser type
        self.riser_type_combo = QComboBox()
        self.riser_type_combo.addItems(["Open", "Closed", "Mixed"])
        stair_layout.addRow("Riser Type:", self.riser_type_combo)

        # Nosing toggle
        self.nosing_checkbox = QCheckBox("Nosing")
        self.nosing_checkbox.setChecked(True)  # Default ON
        stair_layout.addRow("", self.nosing_checkbox)

        # Material type
        self.material_combo = QComboBox()
        self.material_combo.addItems(["LSL", "LVL", "Solid Wood", "Plywood"])
        self.material_combo.setCurrentText("LSL")  # Default
        stair_layout.addRow("Material:", self.material_combo)

        layout.addWidget(stair_group)

        # Landing Group
        landing_group = QGroupBox("Landing")
        landing_layout = QFormLayout(landing_group)

        self.landing_height_edit = QLineEdit("0")
        self.landing_width_edit = QLineEdit("36")
        self.landing_depth_edit = QLineEdit("36")
        self.landing_framing_checkbox = QCheckBox("Include Framing Layout")

        landing_layout.addRow("Landing Height (inches):", self.landing_height_edit)
        landing_layout.addRow("Landing Width (inches):", self.landing_width_edit)
        landing_layout.addRow("Landing Depth (inches):", self.landing_depth_edit)
        landing_layout.addRow("", self.landing_framing_checkbox)

        layout.addWidget(landing_group)

        # Calculate button
        self.calculate_btn = QPushButton("Calculate Stair Geometry")
        self.calculate_btn.clicked.connect(self.calculate_stair)
        self.calculate_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }")
        layout.addWidget(self.calculate_btn)

        layout.addStretch()
        return panel

    def create_results_panel(self):
        """Create the right results panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Tab widget for results
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)

        # Calculations tab
        calc_tab = self.create_calculations_tab()
        tab_widget.addTab(calc_tab, "Calculations")

        # Cut List tab
        cutlist_tab = self.create_cutlist_tab()
        tab_widget.addTab(cutlist_tab, "Cut List")

        # Cost Estimate tab
        cost_tab = self.create_cost_tab()
        tab_widget.addTab(cost_tab, "Cost Estimate")

        # Output Generation buttons
        output_group = QGroupBox("Generate Outputs")
        output_layout = QGridLayout(output_group)

        self.generate_pdf_btn = QPushButton("Generate PDF Layout")
        self.generate_pdf_btn.clicked.connect(self.generate_pdf)
        output_layout.addWidget(self.generate_pdf_btn, 0, 0)

        self.generate_dxf_btn = QPushButton("Generate DXF (Stringers)")
        self.generate_dxf_btn.clicked.connect(self.generate_dxf)
        output_layout.addWidget(self.generate_dxf_btn, 0, 1)

        self.generate_gcode_btn = QPushButton("Generate G-Code")
        self.generate_gcode_btn.clicked.connect(self.generate_gcode)
        output_layout.addWidget(self.generate_gcode_btn, 1, 0)

        self.generate_cutlist_btn = QPushButton("Export Cut List")
        self.generate_cutlist_btn.clicked.connect(self.export_cutlist)
        output_layout.addWidget(self.generate_cutlist_btn, 1, 1)

        layout.addWidget(output_group)

        return panel

    def create_calculations_tab(self):
        """Create calculations display tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Results display
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setFont(QFont("Courier", 10))
        layout.addWidget(self.results_text)

        return widget

    def create_cutlist_tab(self):
        """Create cut list table tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Cut list table
        self.cutlist_table = QTableWidget()
        self.cutlist_table.setColumnCount(6)
        self.cutlist_table.setHorizontalHeaderLabels([
            "Item", "Description", "Material", "Dimensions", "Qty", "Notes"
        ])

        # Make table fill available space
        header = self.cutlist_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        layout.addWidget(self.cutlist_table)

        return widget

    def create_cost_tab(self):
        """Create cost estimate tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Cost breakdown
        self.cost_text = QTextEdit()
        self.cost_text.setReadOnly(True)
        self.cost_text.setFont(QFont("Courier", 10))
        layout.addWidget(self.cost_text)

        return widget

    def calculate_stair(self):
        """Calculate stair geometry and update displays"""
        try:
            # Get parameters from UI
            parameters = self.get_parameters()

            # Calculate stair geometry
            results = self.stair_geometry.calculate_stair(parameters)

            if not results.get('calculations_valid', False):
                error_msg = results.get('error', 'Invalid stair calculations')
                QMessageBox.warning(self, "Calculation Error", error_msg)
                return

            # Update displays
            self.update_calculations_display(results)
            self.update_cutlist_display(results)
            self.update_cost_display(results)

            # Store results for output generation
            self.current_job = results

            self.statusBar().showMessage("Calculations complete - Ready to generate outputs")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Calculation failed: {str(e)}")

    def get_parameters(self) -> Dict:
        """Get current parameters from UI"""
        try:
            return {
                'job_name': self.job_name_edit.text(),
                'customer': self.customer_edit.text(),
                'date': self.date_edit.text(),
                'total_rise': float(self.total_rise_edit.text()),
                'stair_width': float(self.stair_width_edit.text()),
                'tread_thickness': self.tread_thickness_combo.currentText(),
                'riser_type': self.riser_type_combo.currentText(),
                'has_nosing': self.nosing_checkbox.isChecked(),
                'material': self.material_combo.currentText(),
                'landing_height': float(self.landing_height_edit.text() or "0"),
                'landing_width': float(self.landing_width_edit.text() or "36"),
                'landing_depth': float(self.landing_depth_edit.text() or "36"),
                'landing_framing': self.landing_framing_checkbox.isChecked()
            }
        except ValueError as e:
            raise ValueError(f"Invalid parameter value: {e}")

    def update_calculations_display(self, results: Dict):
        """Update the calculations display"""
        rise_run = results.get('rise_run', {})
        stringer_geom = results.get('stringer_geometry', {})

        display_text = []
        display_text.append("STAIR CALCULATIONS")
        display_text.append("=" * 50)
        display_text.append("")

        # Rise and run
        display_text.append("RISE AND RUN:")
        display_text.append(f"  Total Rise: {rise_run.get('total_rise', 0):.2f}\"")
        display_text.append(f"  Total Run: {rise_run.get('total_run', 0):.2f}\"")
        display_text.append(f"  Number of Risers: {rise_run.get('num_risers', 0)}")
        display_text.append(f"  Number of Treads: {rise_run.get('num_treads', 0)}")
        display_text.append(f"  Riser Height: {self.stair_geometry.format_dimension(rise_run.get('riser_height', 0))}")
        display_text.append(f"  Tread Depth: {self.stair_geometry.format_dimension(rise_run.get('tread_depth', 0))}")
        display_text.append("")

        # Validation
        if results.get('calculations_valid'):
            display_text.append("✓ CALCULATIONS MEET BUILDING CODES")
        else:
            display_text.append("⚠ WARNING: May not meet building codes")
        display_text.append("")

        # Stringer info
        display_text.append("STRINGER INFORMATION:")
        display_text.append(f"  Stringer Length: {stringer_geom.get('stringer_length', 0):.2f}\"")
        display_text.append(f"  Number of Stringers: {stringer_geom.get('num_stringers', 3)}")
        display_text.append(f"  Material Length Needed: {stringer_geom.get('material_length_needed', 0)}'")
        display_text.append("")

        # 2R + T rule check
        riser_height = rise_run.get('riser_height', 0)
        tread_depth = rise_run.get('tread_depth', 0)
        two_r_plus_t = 2 * riser_height + tread_depth
        display_text.append("CODE COMPLIANCE:")
        display_text.append(f"  2R + T = {two_r_plus_t:.2f}\" (should be 24-25\")")

        if 24 <= two_r_plus_t <= 25:
            display_text.append("  ✓ Meets 2R + T rule")
        else:
            display_text.append("  ⚠ Does not meet 2R + T rule")

        self.results_text.setPlainText("\n".join(display_text))

    def update_cutlist_display(self, results: Dict):
        """Update the cut list table"""
        cut_list = self.stair_geometry.get_cut_list()

        self.cutlist_table.setRowCount(len(cut_list))

        for row, item in enumerate(cut_list):
            self.cutlist_table.setItem(row, 0, QTableWidgetItem(item.get('item', '')))
            self.cutlist_table.setItem(row, 1, QTableWidgetItem(item.get('description', '')))
            self.cutlist_table.setItem(row, 2, QTableWidgetItem(item.get('material', '')))
            self.cutlist_table.setItem(row, 3, QTableWidgetItem(item.get('dimensions', '')))
            self.cutlist_table.setItem(row, 4, QTableWidgetItem(str(item.get('quantity', 1))))
            self.cutlist_table.setItem(row, 5, QTableWidgetItem(item.get('notes', '')))

    def update_cost_display(self, results: Dict):
        """Update the cost estimate display"""
        cut_list = self.stair_geometry.get_cut_list()
        cost_data = self.cost_estimator.calculate_project_cost(results, cut_list)

        cost_report = self.cost_estimator.generate_cost_report(cost_data)
        self.cost_text.setPlainText(cost_report)

    def generate_pdf(self):
        """Generate PDF layout"""
        if not self.current_job:
            QMessageBox.warning(self, "No Data", "Please calculate stair geometry first")
            return

        try:
            job_name = self.current_job.get('parameters', {}).get('job_name', 'stair_job')
            output_path = f"output/{job_name}_layout.pdf"

            self.pdf_generator.generate_stair_pdf(self.current_job, output_path)

            QMessageBox.information(self, "Success", f"PDF generated: {output_path}")
            self.statusBar().showMessage(f"PDF generated: {output_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to generate PDF: {str(e)}")

    def generate_dxf(self):
        """Generate DXF file for stringers"""
        if not self.current_job:
            QMessageBox.warning(self, "No Data", "Please calculate stair geometry first")
            return

        try:
            job_name = self.current_job.get('parameters', {}).get('job_name', 'stair_job')
            output_path = f"output/{job_name}_stringer.dxf"

            self.dxf_writer.write_stair_dxf(self.current_job, output_path)

            QMessageBox.information(self, "Success", f"DXF generated: {output_path}")
            self.statusBar().showMessage(f"DXF generated: {output_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to generate DXF: {str(e)}")

    def generate_gcode(self):
        """Generate G-Code file"""
        if not self.current_job:
            QMessageBox.warning(self, "No Data", "Please calculate stair geometry first")
            return

        try:
            job_name = self.current_job.get('parameters', {}).get('job_name', 'stair_job')
            output_path = f"output/{job_name}_stringer.nc"

            self.gcode_writer.write_stair_gcode(self.current_job, output_path)

            QMessageBox.information(self, "Success", f"G-Code generated: {output_path}")
            self.statusBar().showMessage(f"G-Code generated: {output_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to generate G-Code: {str(e)}")

    def export_cutlist(self):
        """Export cut list to CSV"""
        if not self.current_job:
            QMessageBox.warning(self, "No Data", "Please calculate stair geometry first")
            return

        try:
            job_name = self.current_job.get('parameters', {}).get('job_name', 'stair_job')
            output_path = f"output/{job_name}_cutlist.csv"

            cut_list = self.stair_geometry.get_cut_list()
            self.cutlist_generator.generate_stair_cutlist(self.current_job, cut_list, output_path)

            QMessageBox.information(self, "Success", f"Cut list exported: {output_path}")
            self.statusBar().showMessage(f"Cut list exported: {output_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to export cut list: {str(e)}")

    def load_defaults(self):
        """Load default values (LSL, 1" treads, nosing ON)"""
        # Already set in UI creation, but can be customized here
        pass

    def new_job(self):
        """Create a new job"""
        self.job_name_edit.setText("Job_001")
        self.customer_edit.clear()
        self.date_edit.setText(datetime.now().strftime("%Y-%m-%d"))
        self.total_rise_edit.setText("108")
        self.stair_width_edit.setText("36")
        self.tread_thickness_combo.setCurrentText("1\"")
        self.riser_type_combo.setCurrentIndex(0)
        self.nosing_checkbox.setChecked(True)
        self.material_combo.setCurrentText("LSL")
        self.landing_height_edit.setText("0")
        self.landing_width_edit.setText("36")
        self.landing_depth_edit.setText("36")
        self.landing_framing_checkbox.setChecked(False)

        # Clear displays
        self.results_text.clear()
        self.cutlist_table.setRowCount(0)
        self.cost_text.clear()
        self.current_job = {}

        self.statusBar().showMessage("New job created")

    def open_job(self):
        """Open a saved job"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Open Job", "jobs", "JSON Files (*.json)"
        )

        if file_path:
            try:
                with open(file_path, 'r') as f:
                    job_data = json.load(f)

                # Load parameters into UI
                params = job_data.get('parameters', {})
                self.job_name_edit.setText(params.get('job_name', ''))
                self.customer_edit.setText(params.get('customer', ''))
                self.date_edit.setText(params.get('date', ''))
                self.total_rise_edit.setText(str(params.get('total_rise', 108)))
                self.stair_width_edit.setText(str(params.get('stair_width', 36)))
                self.tread_thickness_combo.setCurrentText(params.get('tread_thickness', '1\"'))
                self.riser_type_combo.setCurrentText(params.get('riser_type', 'Open'))
                self.nosing_checkbox.setChecked(params.get('has_nosing', True))
                self.material_combo.setCurrentText(params.get('material', 'LSL'))
                self.landing_height_edit.setText(str(params.get('landing_height', 0)))
                self.landing_width_edit.setText(str(params.get('landing_width', 36)))
                self.landing_depth_edit.setText(str(params.get('landing_depth', 36)))
                self.landing_framing_checkbox.setChecked(params.get('landing_framing', False))

                # Recalculate
                self.calculate_stair()

                self.statusBar().showMessage(f"Job loaded: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load job: {str(e)}")

    def save_job(self):
        """Save current job"""
        if not self.current_job:
            QMessageBox.warning(self, "No Data", "No job data to save")
            return

        job_name = self.job_name_edit.text() or "untitled"
        file_path = f"jobs/{job_name}.json"

        os.makedirs("jobs", exist_ok=True)

        try:
            with open(file_path, 'w') as f:
                json.dump(self.current_job, f, indent=2)

            self.statusBar().showMessage(f"Job saved: {file_path}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save job: {str(e)}")

    def save_job_as(self):
        """Save job with new name"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Job As", "jobs", "JSON Files (*.json)"
        )

        if file_path and self.current_job:
            try:
                with open(file_path, 'w') as f:
                    json.dump(self.current_job, f, indent=2)

                self.statusBar().showMessage(f"Job saved: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save job: {str(e)}")

    def duplicate_job(self):
        """Duplicate current job with new name"""
        if not self.current_job:
            QMessageBox.warning(self, "No Data", "No job to duplicate")
            return

        # Create new job name
        current_name = self.job_name_edit.text()
        new_name = f"{current_name}_copy"
        self.job_name_edit.setText(new_name)

        # Update job data
        self.calculate_stair()

        self.statusBar().showMessage(f"Job duplicated as: {new_name}")

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About Stair Manufacturing Tool",
            "Stair Manufacturing Desktop Application\n\n"
            "Automated stair design and CNC prep workflow\n"
            "- Calculate stair geometry\n"
            "- Generate cut lists\n"
            "- Export DXF and G-code\n"
            "- Cost estimation\n\n"
            "Built with PySide6"
        )

    def add_header(self, layout):
        """Add header with logo and title"""
        header_layout = QHBoxLayout()

        # Logo (if available)
        if os.path.exists("assets/logo.png"):
            logo_label = QLabel()
            pixmap = QPixmap("assets/logo.png")
            logo_label.setPixmap(pixmap.scaled(64, 64, Qt.KeepAspectRatio))
            header_layout.addWidget(logo_label)

        # Title
        title_label = QLabel("Stair Manufacturing - Design & CNC Prep Tool")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px; color: #2E86AB;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Company info (placeholder)
        company_label = QLabel("Your Company Name")
        company_label.setStyleSheet("font-size: 12px; color: #666;")
        header_layout.addWidget(company_label)

        layout.addLayout(header_layout)


def main():
    """Main application entry point"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Stair Manufacturing Tool")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Your Company")

    # Create output and jobs directories
    os.makedirs("output", exist_ok=True)
    os.makedirs("jobs", exist_ok=True)
    os.makedirs("config", exist_ok=True)

    # Create and show main window
    window = MainWindow()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
