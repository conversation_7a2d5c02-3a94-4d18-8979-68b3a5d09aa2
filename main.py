#!/usr/bin/env python3
"""
AutoCAD Desktop Application
Main PyQt GUI application for parametric layout generation
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                             QComboBox, QTextEdit, QFileDialog, QMessageBox,
                             QTabWidget, QFormLayout, QSpinBox, QDoubleSpinBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QPixmap, QIcon

from core.geometry import ParametricLayout
from core.dxf_writer import DXFWriter
from core.gcode_writer import GCodeWriter
from core.pdf_generator import PDFGenerator
from core.cutlist_generator import CutlistGenerator


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AutoCAD Desktop - Parametric Layout Generator")
        self.setGeometry(100, 100, 1200, 800)
        
        # Initialize core components
        self.layout_engine = ParametricLayout()
        self.dxf_writer = DXFWriter()
        self.gcode_writer = GCodeWriter()
        self.pdf_generator = PDFGenerator()
        self.cutlist_generator = CutlistGenerator()
        
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Logo/Header
        self.add_header(main_layout)
        
        # Tab widget for different sections
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)
        
        # Add tabs
        tab_widget.addTab(self.create_template_tab(), "Template Selection")
        tab_widget.addTab(self.create_parameters_tab(), "Parameters")
        tab_widget.addTab(self.create_output_tab(), "Output Generation")
        tab_widget.addTab(self.create_preview_tab(), "Preview")
        
        # Status bar
        self.statusBar().showMessage("Ready")
        
    def add_header(self, layout):
        """Add header with logo"""
        header_layout = QHBoxLayout()
        
        # Logo
        if os.path.exists("assets/logo.png"):
            logo_label = QLabel()
            pixmap = QPixmap("assets/logo.png")
            logo_label.setPixmap(pixmap.scaled(64, 64, Qt.KeepAspectRatio))
            header_layout.addWidget(logo_label)
        
        # Title
        title_label = QLabel("AutoCAD Desktop - Parametric Layout Generator")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
    def create_template_tab(self):
        """Create template selection tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Template selection
        form_layout = QFormLayout()
        
        self.template_combo = QComboBox()
        self.template_combo.addItems(["Straight", "L-Shape", "U-Shape"])
        self.template_combo.currentTextChanged.connect(self.on_template_changed)
        form_layout.addRow("Template Type:", self.template_combo)
        
        self.job_name_edit = QLineEdit("myjob")
        form_layout.addRow("Job Name:", self.job_name_edit)
        
        layout.addLayout(form_layout)
        layout.addStretch()
        
        return widget
        
    def create_parameters_tab(self):
        """Create parameters input tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        form_layout = QFormLayout()
        
        # Common parameters
        self.length_spin = QDoubleSpinBox()
        self.length_spin.setRange(0.1, 10000.0)
        self.length_spin.setValue(1000.0)
        self.length_spin.setSuffix(" mm")
        form_layout.addRow("Length:", self.length_spin)
        
        self.width_spin = QDoubleSpinBox()
        self.width_spin.setRange(0.1, 10000.0)
        self.width_spin.setValue(500.0)
        self.width_spin.setSuffix(" mm")
        form_layout.addRow("Width:", self.width_spin)
        
        self.thickness_spin = QDoubleSpinBox()
        self.thickness_spin.setRange(0.1, 100.0)
        self.thickness_spin.setValue(18.0)
        self.thickness_spin.setSuffix(" mm")
        form_layout.addRow("Thickness:", self.thickness_spin)
        
        layout.addLayout(form_layout)
        layout.addStretch()
        
        return widget
        
    def create_output_tab(self):
        """Create output generation tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Output buttons
        button_layout = QVBoxLayout()
        
        self.generate_pdf_btn = QPushButton("Generate PDF Layout")
        self.generate_pdf_btn.clicked.connect(self.generate_pdf)
        button_layout.addWidget(self.generate_pdf_btn)
        
        self.generate_dxf_btn = QPushButton("Generate DXF File")
        self.generate_dxf_btn.clicked.connect(self.generate_dxf)
        button_layout.addWidget(self.generate_dxf_btn)
        
        self.generate_gcode_btn = QPushButton("Generate G-Code")
        self.generate_gcode_btn.clicked.connect(self.generate_gcode)
        button_layout.addWidget(self.generate_gcode_btn)
        
        self.generate_cutlist_btn = QPushButton("Generate Cut List")
        self.generate_cutlist_btn.clicked.connect(self.generate_cutlist)
        button_layout.addWidget(self.generate_cutlist_btn)
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        return widget
        
    def create_preview_tab(self):
        """Create preview tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        layout.addWidget(self.preview_text)
        
        return widget
        
    def on_template_changed(self, template_name):
        """Handle template selection change"""
        self.statusBar().showMessage(f"Template changed to: {template_name}")
        
    def generate_pdf(self):
        """Generate PDF layout"""
        try:
            job_name = self.job_name_edit.text()
            params = self.get_parameters()
            
            output_path = f"output/{job_name}_layout.pdf"
            self.pdf_generator.generate(params, output_path)
            
            QMessageBox.information(self, "Success", f"PDF generated: {output_path}")
            self.statusBar().showMessage(f"PDF generated: {output_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to generate PDF: {str(e)}")
            
    def generate_dxf(self):
        """Generate DXF file"""
        try:
            job_name = self.job_name_edit.text()
            params = self.get_parameters()
            
            output_path = f"output/{job_name}_stringer.dxf"
            self.dxf_writer.write(params, output_path)
            
            QMessageBox.information(self, "Success", f"DXF generated: {output_path}")
            self.statusBar().showMessage(f"DXF generated: {output_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to generate DXF: {str(e)}")
            
    def generate_gcode(self):
        """Generate G-Code file"""
        try:
            job_name = self.job_name_edit.text()
            params = self.get_parameters()
            
            output_path = f"output/{job_name}_stringer.nc"
            self.gcode_writer.write(params, output_path)
            
            QMessageBox.information(self, "Success", f"G-Code generated: {output_path}")
            self.statusBar().showMessage(f"G-Code generated: {output_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to generate G-Code: {str(e)}")
            
    def generate_cutlist(self):
        """Generate cut list"""
        try:
            job_name = self.job_name_edit.text()
            params = self.get_parameters()
            
            output_path = f"output/{job_name}_cutlist.csv"
            self.cutlist_generator.generate(params, output_path)
            
            QMessageBox.information(self, "Success", f"Cut list generated: {output_path}")
            self.statusBar().showMessage(f"Cut list generated: {output_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to generate cut list: {str(e)}")
            
    def get_parameters(self):
        """Get current parameters from UI"""
        return {
            'template': self.template_combo.currentText().lower(),
            'job_name': self.job_name_edit.text(),
            'length': self.length_spin.value(),
            'width': self.width_spin.value(),
            'thickness': self.thickness_spin.value()
        }


def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    
    # Create output directory if it doesn't exist
    os.makedirs("output", exist_ok=True)
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
