{"name": "Straight Stair Configuration", "description": "Standard straight stair with configurable rise, run, and width", "type": "straight_stair", "category": "stairs", "parameters": {"total_rise": {"type": "float", "default": 108.0, "min": 60.0, "max": 144.0, "unit": "inches", "description": "Total vertical rise of the stair", "step": 0.125}, "stair_width": {"type": "float", "default": 36.0, "min": 24.0, "max": 60.0, "unit": "inches", "description": "Width of the stair", "step": 1.0}, "tread_thickness": {"type": "string", "default": "1\"", "options": ["3/4\"", "1\"", "1-1/4\"", "1-1/2\"", "2\""], "description": "Thickness of tread material"}, "material": {"type": "string", "default": "LSL", "options": ["LSL", "LVL", "Solid Wood", "Plywood"], "description": "Stringer material type"}, "has_nosing": {"type": "boolean", "default": true, "description": "Include nosing on treads"}, "nosing_depth": {"type": "float", "default": 1.0, "min": 0.5, "max": 1.5, "unit": "inches", "description": "Depth of nosing overhang", "depends_on": "has_nosing"}, "riser_type": {"type": "string", "default": "Open", "options": ["Open", "Closed", "Mixed"], "description": "Type of riser configuration"}, "stringer_material_grade": {"type": "string", "default": "Construction", "options": ["Construction", "Select", "Premium"], "description": "Grade of stringer material"}}, "calculations": {"optimal_riser_height": "total_rise / round(total_rise / 7.5)", "num_risers": "round(total_rise / optimal_riser_height)", "num_treads": "num_risers - 1", "tread_depth": "25 - (2 * optimal_riser_height)", "total_run": "tread_depth * num_treads", "stringer_length": "sqrt(total_run^2 + total_rise^2)", "stringer_count": "stair_width <= 36 ? 3 : ceil(stair_width / 16)", "material_length_needed": "ceil(stringer_length / 12) * 12"}, "geometry": {"stringers": {"type": "array", "count": "{{stringer_count}}", "spacing": "{{stair_width / (stringer_count - 1)}}", "element": {"type": "stringer_profile", "material": "{{material}}", "width": 11.25, "thickness": 1.5, "length": "{{stringer_length}}", "profile_type": "sawtooth", "cut_points": "{{generate_stringer_cuts()}}"}}, "treads": {"type": "array", "count": "{{num_treads}}", "element": {"type": "rectangle", "width": "{{stair_width}}", "depth": "{{tread_depth + (has_nosing ? nosing_depth : 0)}}", "thickness": "{{tread_thickness}}", "nosing": "{{has_nosing}}", "position": {"step": "{{index + 1}}"}}}, "risers": {"type": "array", "count": "{{riser_type == 'Closed' ? num_risers : 0}}", "element": {"type": "rectangle", "width": "{{stair_width}}", "height": "{{optimal_riser_height}}", "thickness": "0.75", "position": {"step": "{{index + 1}}"}}}}, "cut_list": [{"name": "Stringers", "material": "{{material}}", "description": "2x12 stringer material - {{stringer_material_grade}} grade", "thickness": "1-1/2\"", "width": "11-1/4\"", "length": "{{material_length_needed}}'", "quantity": "{{stringer_count}}", "notes": "Cut to stair profile using DXF template", "unit_cost": "{{get_material_cost(material, 'board_foot')}}", "board_feet": "{{(1.5 * 11.25 * material_length_needed * 12) / 144}}"}, {"name": "Treads", "material": "Hardwood", "description": "Tread boards - {{tread_thickness}} thick", "thickness": "{{tread_thickness}}", "width": "{{stair_width}}\"", "length": "{{tread_depth + (has_nosing ? nosing_depth : 0)}}\"", "quantity": "{{num_treads}}", "notes": "{{has_nosing ? 'Include nosing profile' : 'Square edge'}}", "unit_cost": "{{get_material_cost('Hardwood', 'board_foot')}}", "board_feet": "{{parse_thickness(tread_thickness) * stair_width * (tread_depth + (has_nosing ? nosing_depth : 0)) * num_treads / 144}}"}, {"name": "Risers", "material": "Plywood", "description": "3/4\" plywood risers", "thickness": "3/4\"", "width": "{{stair_width}}\"", "height": "{{optimal_riser_height}}\"", "quantity": "{{riser_type == 'Closed' ? num_risers : 0}}", "notes": "Only needed for closed riser configuration", "unit_cost": "{{get_material_cost('Plywood', 'sheet')}}", "sheets_needed": "{{ceil((stair_width * optimal_riser_height * num_risers) / (48 * 96))}}"}, {"name": "Hardware", "material": "Steel", "description": "Screws, bolts, and brackets", "quantity": 1, "notes": "Mounting hardware for stringers and treads", "unit_cost": "{{get_hardware_cost('stair_hardware')}}"}], "building_codes": {"max_riser_height": 7.75, "min_riser_height": 7.0, "max_tread_depth": 11.0, "min_tread_depth": 10.0, "max_riser_variation": 0.375, "two_r_plus_t_min": 24.0, "two_r_plus_t_max": 25.0, "min_headroom": 80.0, "min_stair_width": 36.0}, "validation_rules": [{"rule": "optimal_riser_height >= building_codes.min_riser_height && optimal_riser_height <= building_codes.max_riser_height", "message": "Riser height must be between 7\" and 7.75\""}, {"rule": "tread_depth >= building_codes.min_tread_depth && tread_depth <= building_codes.max_tread_depth", "message": "Tread depth must be between 10\" and 11\""}, {"rule": "(2 * optimal_riser_height + tread_depth) >= building_codes.two_r_plus_t_min && (2 * optimal_riser_height + tread_depth) <= building_codes.two_r_plus_t_max", "message": "2R + T rule: must be between 24\" and 25\""}, {"rule": "stair_width >= building_codes.min_stair_width", "message": "Stair width must be at least 36\""}], "output_settings": {"dxf": {"layers": {"stringers": "STRINGER_OUTLINE", "cut_lines": "CUT_LINES", "dimensions": "DIMENSIONS", "text": "TEXT"}, "scale": 1.0, "units": "inches"}, "gcode": {"tool_diameter": 0.5, "feed_rate": 150, "plunge_rate": 50, "spindle_speed": 18000, "cut_depth": -1.6, "safe_height": 0.5}, "pdf": {"page_size": "letter", "orientation": "portrait", "scale": "fit_to_page", "include_title_block": true, "include_cut_list": true, "include_dimensions": true}}}