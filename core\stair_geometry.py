"""
Stair Geometry Calculation Module
Handles all stair geometry calculations including rise/run, treads, risers, and stringers
"""

import math
from typing import Dict, List, Tuple, Any
from fractions import Fraction


class StairGeometry:
    """Main class for stair geometry calculations"""
    
    def __init__(self):
        # Standard building code constraints
        self.min_tread_depth = 10.0  # inches
        self.max_tread_depth = 11.0  # inches
        self.min_riser_height = 7.0  # inches
        self.max_riser_height = 7.75  # inches
        self.max_riser_variation = 0.375  # 3/8 inch
        
        # Default nosing
        self.nosing_depth = 1.0  # inches
        
        # Current calculation results
        self.results = {}
        
    def calculate_stair(self, parameters: Dict) -> Dict:
        """Calculate complete stair geometry"""
        try:
            # Extract parameters
            total_rise = float(parameters.get('total_rise', 108))
            stair_width = float(parameters.get('stair_width', 36))
            tread_thickness = self._parse_fraction(parameters.get('tread_thickness', '1'))
            riser_type = parameters.get('riser_type', 'Open')
            has_nosing = parameters.get('has_nosing', True)
            material = parameters.get('material', 'LSL')
            
            # Landing parameters
            landing_height = float(parameters.get('landing_height', 0))
            landing_width = float(parameters.get('landing_width', 36))
            landing_depth = float(parameters.get('landing_depth', 36))
            
            # Calculate optimal rise and run
            rise_run = self._calculate_optimal_rise_run(total_rise)
            
            # Calculate stringer geometry
            stringer_geometry = self._calculate_stringer_geometry(
                rise_run, stair_width, tread_thickness, has_nosing
            )
            
            # Calculate landing if needed
            landing_geometry = None
            if landing_height > 0:
                landing_geometry = self._calculate_landing_geometry(
                    landing_width, landing_depth, landing_height
                )
            
            # Compile results
            self.results = {
                'parameters': parameters,
                'rise_run': rise_run,
                'stringer_geometry': stringer_geometry,
                'landing_geometry': landing_geometry,
                'material': material,
                'riser_type': riser_type,
                'calculations_valid': self._validate_calculations(rise_run)
            }
            
            return self.results
            
        except Exception as e:
            return {'error': str(e), 'calculations_valid': False}
            
    def _calculate_optimal_rise_run(self, total_rise: float) -> Dict:
        """Calculate optimal rise and run based on building codes"""
        # Try different numbers of risers to find optimal solution
        best_solution = None
        best_score = float('inf')
        
        # Test riser counts from reasonable range
        min_risers = math.ceil(total_rise / self.max_riser_height)
        max_risers = math.floor(total_rise / self.min_riser_height)
        
        for num_risers in range(min_risers, max_risers + 1):
            riser_height = total_rise / num_risers
            
            # Calculate tread depth using 2R + T = 25 rule (approximately)
            tread_depth = 25 - (2 * riser_height)
            
            # Check if within acceptable range
            if (self.min_tread_depth <= tread_depth <= self.max_tread_depth and
                self.min_riser_height <= riser_height <= self.max_riser_height):
                
                # Score based on how close to ideal (7.5" rise, 10" tread)
                score = abs(riser_height - 7.5) + abs(tread_depth - 10.0)
                
                if score < best_score:
                    best_score = score
                    best_solution = {
                        'num_risers': num_risers,
                        'num_treads': num_risers - 1,  # One less tread than risers
                        'riser_height': riser_height,
                        'tread_depth': tread_depth,
                        'total_run': tread_depth * (num_risers - 1),
                        'total_rise': total_rise
                    }
        
        if best_solution is None:
            # Fallback - use closest valid solution
            num_risers = round(total_rise / 7.5)
            riser_height = total_rise / num_risers
            tread_depth = 25 - (2 * riser_height)
            
            best_solution = {
                'num_risers': num_risers,
                'num_treads': num_risers - 1,
                'riser_height': riser_height,
                'tread_depth': tread_depth,
                'total_run': tread_depth * (num_risers - 1),
                'total_rise': total_rise,
                'warning': 'Solution may not meet building codes'
            }
            
        return best_solution
        
    def _calculate_stringer_geometry(self, rise_run: Dict, stair_width: float, 
                                   tread_thickness: float, has_nosing: bool) -> Dict:
        """Calculate stringer cutting geometry"""
        num_treads = rise_run['num_treads']
        num_risers = rise_run['num_risers']
        riser_height = rise_run['riser_height']
        tread_depth = rise_run['tread_depth']
        
        # Calculate stringer length
        total_run = rise_run['total_run']
        total_rise = rise_run['total_rise']
        stringer_length = math.sqrt(total_run**2 + total_rise**2)
        
        # Calculate cut points for stringer
        cut_points = []
        
        for i in range(num_risers):
            # Each step cut point
            x = i * tread_depth
            y = i * riser_height
            
            # Add tread cut (horizontal)
            if i < num_treads:
                tread_cut = {
                    'type': 'tread',
                    'start': (x, y),
                    'end': (x + tread_depth, y),
                    'step_number': i + 1
                }
                cut_points.append(tread_cut)
            
            # Add riser cut (vertical)
            if i < num_risers:
                riser_cut = {
                    'type': 'riser',
                    'start': (x + tread_depth, y),
                    'end': (x + tread_depth, y + riser_height),
                    'step_number': i + 1
                }
                cut_points.append(riser_cut)
        
        # Calculate material requirements
        # Standard stringer material: 2x12 or 2x14
        stringer_width = 11.25  # 2x12 actual width
        stringer_thickness = 1.5  # 2x actual thickness
        
        # Number of stringers needed (typically 3 for up to 36" width)
        num_stringers = 3 if stair_width <= 36 else math.ceil(stair_width / 16)
        
        return {
            'stringer_length': stringer_length,
            'stringer_width': stringer_width,
            'stringer_thickness': stringer_thickness,
            'num_stringers': num_stringers,
            'cut_points': cut_points,
            'material_length_needed': math.ceil(stringer_length / 12) * 12,  # Round up to nearest foot
            'tread_thickness': tread_thickness,
            'has_nosing': has_nosing,
            'nosing_depth': self.nosing_depth if has_nosing else 0
        }
        
    def _calculate_landing_geometry(self, width: float, depth: float, height: float) -> Dict:
        """Calculate landing framing geometry"""
        # Standard framing: 2x10 or 2x12 joists
        joist_spacing = 16  # inches on center
        joist_width = 9.25  # 2x10 actual width
        joist_thickness = 1.5
        
        # Calculate number of joists needed
        num_joists = math.ceil(depth / joist_spacing) + 1
        
        # Rim board and blocking
        rim_board_length = 2 * (width + depth)
        
        return {
            'width': width,
            'depth': depth,
            'height': height,
            'joist_spacing': joist_spacing,
            'joist_width': joist_width,
            'joist_thickness': joist_thickness,
            'num_joists': num_joists,
            'joist_length': width,
            'rim_board_length': rim_board_length,
            'decking_area': width * depth
        }
        
    def _validate_calculations(self, rise_run: Dict) -> bool:
        """Validate calculations against building codes"""
        riser_height = rise_run['riser_height']
        tread_depth = rise_run['tread_depth']
        
        # Check basic constraints
        if not (self.min_riser_height <= riser_height <= self.max_riser_height):
            return False
            
        if not (self.min_tread_depth <= tread_depth <= self.max_tread_depth):
            return False
            
        # Check 2R + T rule (should be between 24" and 25")
        two_r_plus_t = 2 * riser_height + tread_depth
        if not (24 <= two_r_plus_t <= 25):
            return False
            
        return True
        
    def _parse_fraction(self, fraction_str: str) -> float:
        """Parse fraction string like '1-1/4' to decimal"""
        try:
            if '-' in fraction_str:
                # Handle mixed numbers like "1-1/4"
                parts = fraction_str.split('-')
                whole = int(parts[0])
                frac = Fraction(parts[1])
                return whole + float(frac)
            elif '/' in fraction_str:
                # Handle simple fractions like "3/4"
                return float(Fraction(fraction_str))
            else:
                # Handle simple decimals or integers
                return float(fraction_str.replace('"', ''))
        except:
            return 1.0  # Default fallback
            
    def format_dimension(self, decimal_inches: float) -> str:
        """Format decimal inches to inches and fractions"""
        whole_inches = int(decimal_inches)
        remainder = decimal_inches - whole_inches
        
        # Convert to nearest 1/16"
        sixteenths = round(remainder * 16)
        
        if sixteenths == 0:
            return f"{whole_inches}\""
        elif sixteenths == 16:
            return f"{whole_inches + 1}\""
        else:
            # Simplify fraction
            frac = Fraction(sixteenths, 16)
            if whole_inches == 0:
                return f"{frac}\""
            else:
                return f"{whole_inches}-{frac}\""
                
    def get_cut_list(self) -> List[Dict]:
        """Generate cut list from current calculations"""
        if not self.results or not self.results.get('calculations_valid'):
            return []
            
        cut_list = []
        
        # Stringers
        stringer_geom = self.results['stringer_geometry']
        cut_list.append({
            'item': 'Stringers',
            'description': f"2x12 x {stringer_geom['material_length_needed']}'",
            'material': self.results['material'],
            'dimensions': f"1-1/2\" x 11-1/4\" x {stringer_geom['material_length_needed']}'",
            'quantity': stringer_geom['num_stringers'],
            'notes': 'Cut to stair profile'
        })
        
        # Treads
        rise_run = self.results['rise_run']
        tread_width = self.results['parameters']['stair_width']
        tread_thickness = stringer_geom['tread_thickness']
        
        cut_list.append({
            'item': 'Treads',
            'description': f"Tread material {self.format_dimension(tread_thickness)} thick",
            'material': 'Hardwood/Engineered',
            'dimensions': f"{self.format_dimension(tread_thickness)} x {tread_width}\" x {self.format_dimension(rise_run['tread_depth'] + stringer_geom['nosing_depth'])}\"",
            'quantity': rise_run['num_treads'],
            'notes': 'Include nosing if specified'
        })
        
        # Landing framing (if applicable)
        if self.results['landing_geometry']:
            landing = self.results['landing_geometry']
            
            cut_list.append({
                'item': 'Landing Joists',
                'description': f"2x10 x {math.ceil(landing['joist_length']/12)}'",
                'material': 'Lumber',
                'dimensions': f"1-1/2\" x 9-1/4\" x {landing['joist_length']}\"",
                'quantity': landing['num_joists'],
                'notes': 'Landing frame joists'
            })
            
            cut_list.append({
                'item': 'Landing Rim Board',
                'description': "2x10 rim board",
                'material': 'Lumber',
                'dimensions': f"1-1/2\" x 9-1/4\" x various",
                'quantity': 1,
                'notes': f"Total length: {landing['rim_board_length']}\" (cut to fit)"
            })
        
        return cut_list
