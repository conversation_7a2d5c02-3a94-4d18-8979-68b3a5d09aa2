#!/usr/bin/env python3
"""
Demo script showing how to use the Template Manager
This demonstrates the template system without the GUI
"""

from core.template_manager import TemplateManager
import j<PERSON>

def demo_template_manager():
    """Demonstrate template manager functionality"""
    print("Template Manager Demo")
    print("=" * 50)
    
    # Initialize template manager
    tm = TemplateManager()
    
    # 1. List available templates
    print("\n1. Available Templates:")
    print("-" * 30)
    templates = tm.get_template_list()
    for template in templates:
        print(f"  • {template['display_name']} ({template['name']})")
        print(f"    Type: {template['type']}")
        print(f"    Description: {template['description']}")
        print()
    
    # 2. Get stair-specific templates
    print("\n2. Stair Templates:")
    print("-" * 30)
    stair_templates = tm.get_stair_templates()
    for template in stair_templates:
        print(f"  • {template['display_name']}")
        print(f"    Type: {template['type']}")
        print()
    
    # 3. Demonstrate template parameter processing
    print("\n3. Template Parameter Processing:")
    print("-" * 30)
    
    # Example user input
    user_params = {
        'total_rise': 96.0,  # 8 feet
        'stair_width': 42.0,  # 42 inches
        'tread_thickness': '1-1/4"',
        'material': 'LVL',
        'has_nosing': True,
        'riser_type': 'Open'
    }
    
    print("User Parameters:")
    for key, value in user_params.items():
        print(f"  {key}: {value}")
    
    # Process with straight stair template
    template_name = 'straight_stair'
    if tm.get_template(template_name):
        print(f"\nProcessing with template: {template_name}")
        
        try:
            processed_params = tm.process_template_parameters(template_name, user_params)
            
            print("\nProcessed Parameters (including calculations):")
            for key, value in processed_params.items():
                if isinstance(value, float):
                    print(f"  {key}: {value:.3f}")
                else:
                    print(f"  {key}: {value}")
                    
        except Exception as e:
            print(f"Error processing template: {e}")
    
    # 4. Generate cut list from template
    print("\n4. Template Cut List Generation:")
    print("-" * 30)
    
    if tm.get_template(template_name):
        try:
            processed_params = tm.process_template_parameters(template_name, user_params)
            cut_list = tm.generate_cut_list(template_name, processed_params)
            
            print("Generated Cut List:")
            for i, item in enumerate(cut_list, 1):
                print(f"\n  Item {i}: {item.get('name', 'Unknown')}")
                print(f"    Material: {item.get('material', 'N/A')}")
                print(f"    Description: {item.get('description', 'N/A')}")
                print(f"    Dimensions: {item.get('dimensions', 'N/A')}")
                print(f"    Quantity: {item.get('quantity', 'N/A')}")
                if 'notes' in item:
                    print(f"    Notes: {item['notes']}")
                    
        except Exception as e:
            print(f"Error generating cut list: {e}")
    
    # 5. Validate parameters
    print("\n5. Parameter Validation:")
    print("-" * 30)
    
    # Test with invalid parameters
    invalid_params = user_params.copy()
    invalid_params['total_rise'] = 200.0  # Too high
    invalid_params['stair_width'] = 20.0  # Too narrow
    
    if tm.get_template(template_name):
        validation_errors = tm.validate_parameters(template_name, invalid_params)
        
        if validation_errors:
            print("Validation Errors Found:")
            for error in validation_errors:
                print(f"  ❌ {error}")
        else:
            print("✓ All parameters valid")
    
    # 6. Get UI configuration
    print("\n6. UI Configuration for Template:")
    print("-" * 30)
    
    if tm.get_template(template_name):
        ui_config = tm.get_template_parameters_ui_config(template_name)
        
        if ui_config and 'parameters' in ui_config:
            print("UI Parameter Configuration:")
            for param in ui_config['parameters']:
                print(f"\n  Parameter: {param['name']}")
                print(f"    Label: {param['label']}")
                print(f"    Type: {param['type']}")
                print(f"    Default: {param['default']}")
                if param.get('min') is not None:
                    print(f"    Range: {param['min']} - {param['max']}")
                if param.get('options'):
                    print(f"    Options: {param['options']}")
                if param.get('unit'):
                    print(f"    Unit: {param['unit']}")

def demo_ui_integration():
    """Show how this integrates with the UI"""
    print("\n\nUI Integration Example")
    print("=" * 50)
    
    print("""
How the Template Manager integrates with the UI:

1. TEMPLATE SELECTION:
   - ComboBox populated with tm.get_stair_templates()
   - User selects template type (Straight, L-Shape, U-Shape)
   
2. PARAMETER LOADING:
   - When template changes: on_template_changed()
   - Load defaults: tm.get_template_parameters_ui_config()
   - Update UI controls with template defaults
   
3. PARAMETER VALIDATION:
   - Before calculation: tm.validate_parameters()
   - Show validation errors to user
   
4. CALCULATION:
   - Process parameters: tm.process_template_parameters()
   - Includes template calculations (rise/run, stringer length, etc.)
   
5. CUT LIST GENERATION:
   - Generate from template: tm.generate_cut_list()
   - Display in table with proper formatting
   
6. DYNAMIC UI:
   - Show/hide parameters based on template
   - Update parameter ranges and options
   - Conditional parameters (e.g., nosing depth only if nosing enabled)

Example UI Code:
```python
# In MainWindow.__init__():
self.template_manager = TemplateManager()
self.populate_template_combo()

# Template selection handler:
def on_template_changed(self):
    template_name = self.template_combo.currentData()
    ui_config = self.template_manager.get_template_parameters_ui_config(template_name)
    self.update_ui_from_template(ui_config)

# Calculation with template:
def calculate_stair(self):
    params = self.get_parameters()
    template_name = self.template_combo.currentData()
    
    # Validate
    errors = self.template_manager.validate_parameters(template_name, params)
    if errors:
        show_validation_errors(errors)
        return
    
    # Process
    processed_params = self.template_manager.process_template_parameters(template_name, params)
    
    # Calculate
    results = self.stair_geometry.calculate_stair(processed_params)
    
    # Generate cut list
    cut_list = self.template_manager.generate_cut_list(template_name, processed_params)
```
    """)

if __name__ == "__main__":
    demo_template_manager()
    demo_ui_integration()
