"""
DXF Writer Module
Generates DXF files for stair stringers and CNC machining
"""

import os
from typing import Dict, List, Tuple, Any
try:
    import ezdxf
    EZDXF_AVAILABLE = True
except ImportError:
    EZDXF_AVAILABLE = False


class DXFWriter:
    """Class for writing DXF files for stair stringers"""

    def __init__(self):
        pass

    def write_stair_dxf(self, stair_results: Dict, output_path: str):
        """Write DXF file for stair stringers"""
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        if EZDXF_AVAILABLE:
            self._write_with_ezdxf(stair_results, output_path)
        else:
            self._write_basic_dxf(stair_results, output_path)

    def _write_with_ezdxf(self, stair_results: Dict, output_path: str):
        """Write DXF using ezdxf library (preferred method)"""
        # Create new DXF document
        doc = ezdxf.new('R2010')  # AutoCAD 2010 format
        msp = doc.modelspace()

        # Add layers
        doc.layers.new('STRINGER_OUTLINE', dxfattribs={'color': 1})  # Red
        doc.layers.new('CUT_LINES', dxfattribs={'color': 2})  # Yellow
        doc.layers.new('DIMENSIONS', dxfattribs={'color': 3})  # Green
        doc.layers.new('TEXT', dxfattribs={'color': 7})  # White/Black

        # Get stringer geometry
        stringer_geom = stair_results.get('stringer_geometry', {})
        rise_run = stair_results.get('rise_run', {})

        if not stringer_geom or not rise_run:
            return

        # Draw stringer outline
        self._draw_stringer_outline_ezdxf(msp, stringer_geom, rise_run)

        # Draw cut lines
        self._draw_cut_lines_ezdxf(msp, stringer_geom)

        # Add dimensions
        self._add_dimensions_ezdxf(msp, rise_run)

        # Add title block
        self._add_title_block_ezdxf(msp, stair_results)

        # Save DXF file
        doc.saveas(output_path)

    def _draw_stringer_outline_ezdxf(self, msp, stringer_geom: Dict, rise_run: Dict):
        """Draw stringer outline using ezdxf"""
        if not EZDXF_AVAILABLE:
            return

        # Stringer dimensions
        length = stringer_geom.get('stringer_length', 120)
        width = stringer_geom.get('stringer_width', 11.25)

        # Draw stringer board outline
        points = [
            (0, 0),
            (length, 0),
            (length, width),
            (0, width),
            (0, 0)
        ]

        msp.add_lwpolyline(points, close=True, dxfattribs={'layer': 'STRINGER_OUTLINE'})

    def _draw_cut_lines_ezdxf(self, msp, stringer_geom: Dict):
        """Draw step cut lines using ezdxf"""
        if not EZDXF_AVAILABLE:
            return

        cut_points = stringer_geom.get('cut_points', [])

        for cut in cut_points:
            start = cut['start']
            end = cut['end']

            # Draw cut line
            msp.add_line(start, end, dxfattribs={'layer': 'CUT_LINES'})

    def _add_dimensions_ezdxf(self, msp, rise_run: Dict):
        """Add dimensions using ezdxf"""
        if not EZDXF_AVAILABLE:
            return

        # Add key dimensions as text (simplified)
        riser_height = rise_run.get('riser_height', 7.5)
        tread_depth = rise_run.get('tread_depth', 10)

        # Add dimension text
        msp.add_text(
            f"Riser: {riser_height:.2f}\"",
            dxfattribs={
                'layer': 'DIMENSIONS',
                'height': 0.25,
                'insert': (5, -2)
            }
        )

        msp.add_text(
            f"Tread: {tread_depth:.2f}\"",
            dxfattribs={
                'layer': 'DIMENSIONS',
                'height': 0.25,
                'insert': (5, -3)
            }
        )

    def _add_title_block_ezdxf(self, msp, stair_results: Dict):
        """Add title block using ezdxf"""
        if not EZDXF_AVAILABLE:
            return

        # Title block position (bottom right)
        x_pos = 20
        y_pos = -8

        # Job information
        job_name = stair_results.get('parameters', {}).get('job_name', 'Stair Job')

        msp.add_text(
            f"Job: {job_name}",
            dxfattribs={
                'layer': 'TEXT',
                'height': 0.3,
                'insert': (x_pos, y_pos)
            }
        )

        msp.add_text(
            "Stair Stringer Template",
            dxfattribs={
                'layer': 'TEXT',
                'height': 0.25,
                'insert': (x_pos, y_pos - 1)
            }
        )

    def _write_basic_dxf(self, stair_results: Dict, output_path: str):
        """Write basic DXF without ezdxf library (fallback)"""
        with open(output_path, 'w') as f:
            self._write_dxf_header(f)
            self._write_stair_entities(f, stair_results)
            self._write_dxf_footer(f)
            
    def _write_dxf_header(self, file):
        """Write DXF file header"""
        file.write("0\n")
        file.write("SECTION\n")
        file.write("2\n")
        file.write("HEADER\n")
        file.write("9\n")
        file.write("$ACADVER\n")
        file.write("1\n")
        file.write("AC1015\n")
        file.write("9\n")
        file.write("$HANDSEED\n")
        file.write("5\n")
        file.write("FFFF\n")
        file.write("0\n")
        file.write("ENDSEC\n")
        
        # Tables section
        file.write("0\n")
        file.write("SECTION\n")
        file.write("2\n")
        file.write("TABLES\n")
        
        # Layer table
        file.write("0\n")
        file.write("TABLE\n")
        file.write("2\n")
        file.write("LAYER\n")
        file.write("5\n")
        file.write("2\n")
        file.write("100\n")
        file.write("AcDbSymbolTable\n")
        file.write("70\n")
        file.write("2\n")
        
        # Default layer
        file.write("0\n")
        file.write("LAYER\n")
        file.write("5\n")
        file.write("10\n")
        file.write("100\n")
        file.write("AcDbSymbolTableRecord\n")
        file.write("100\n")
        file.write("AcDbLayerTableRecord\n")
        file.write("2\n")
        file.write("0\n")
        file.write("70\n")
        file.write("0\n")
        file.write("6\n")
        file.write("CONTINUOUS\n")
        
        # Stringer layer
        file.write("0\n")
        file.write("LAYER\n")
        file.write("5\n")
        file.write("11\n")
        file.write("100\n")
        file.write("AcDbSymbolTableRecord\n")
        file.write("100\n")
        file.write("AcDbLayerTableRecord\n")
        file.write("2\n")
        file.write("STRINGERS\n")
        file.write("70\n")
        file.write("0\n")
        file.write("6\n")
        file.write("CONTINUOUS\n")
        file.write("62\n")
        file.write("1\n")  # Red color
        
        file.write("0\n")
        file.write("ENDTAB\n")
        file.write("0\n")
        file.write("ENDSEC\n")
        
        # Entities section header
        file.write("0\n")
        file.write("SECTION\n")
        file.write("2\n")
        file.write("ENTITIES\n")
        
    def _write_dxf_entities(self, file, geometry: Dict):
        """Write DXF entities from geometry"""
        for element_name, element in geometry.items():
            if element_name.startswith('stringer'):
                layer = "STRINGERS"
            else:
                layer = "0"
                
            self._write_geometry_element(file, element, layer)
            
    def _write_geometry_element(self, file, element: Dict, layer: str = "0"):
        """Write a single geometry element to DXF"""
        element_type = element.get('type', 'rectangle')
        
        if element_type == 'rectangle':
            self._write_rectangle(file, element, layer)
        elif element_type == 'l_shape':
            self._write_l_shape(file, element, layer)
        elif element_type == 'u_shape':
            self._write_u_shape(file, element, layer)
        elif element_type == 'array':
            for sub_element in element.get('elements', []):
                self._write_geometry_element(file, sub_element, layer)
                
    def _write_rectangle(self, file, element: Dict, layer: str):
        """Write a rectangle as a polyline"""
        vertices = element.get('vertices', [])
        if not vertices:
            return
            
        # Write as LWPOLYLINE
        file.write("0\n")
        file.write("LWPOLYLINE\n")
        file.write("5\n")
        file.write("100\n")  # Handle (simplified)
        file.write("100\n")
        file.write("AcDbEntity\n")
        file.write("8\n")
        file.write(f"{layer}\n")
        file.write("100\n")
        file.write("AcDbPolyline\n")
        file.write("90\n")
        file.write(f"{len(vertices)}\n")
        file.write("70\n")
        file.write("1\n")  # Closed polyline
        
        # Write vertices
        for x, y in vertices:
            file.write("10\n")
            file.write(f"{x:.3f}\n")
            file.write("20\n")
            file.write(f"{y:.3f}\n")
            
    def _write_l_shape(self, file, element: Dict, layer: str):
        """Write an L-shape as a polyline"""
        vertices = element.get('vertices', [])
        if not vertices:
            return
            
        # Write as LWPOLYLINE
        file.write("0\n")
        file.write("LWPOLYLINE\n")
        file.write("5\n")
        file.write("101\n")  # Handle (simplified)
        file.write("100\n")
        file.write("AcDbEntity\n")
        file.write("8\n")
        file.write(f"{layer}\n")
        file.write("100\n")
        file.write("AcDbPolyline\n")
        file.write("90\n")
        file.write(f"{len(vertices)}\n")
        file.write("70\n")
        file.write("1\n")  # Closed polyline
        
        # Write vertices
        for x, y in vertices:
            file.write("10\n")
            file.write(f"{x:.3f}\n")
            file.write("20\n")
            file.write(f"{y:.3f}\n")
            
    def _write_u_shape(self, file, element: Dict, layer: str):
        """Write a U-shape as polylines (outer and inner)"""
        vertices = element.get('vertices', {})
        
        # Write outer boundary
        outer_vertices = vertices.get('outer', [])
        if outer_vertices:
            file.write("0\n")
            file.write("LWPOLYLINE\n")
            file.write("5\n")
            file.write("102\n")  # Handle (simplified)
            file.write("100\n")
            file.write("AcDbEntity\n")
            file.write("8\n")
            file.write(f"{layer}\n")
            file.write("100\n")
            file.write("AcDbPolyline\n")
            file.write("90\n")
            file.write(f"{len(outer_vertices)}\n")
            file.write("70\n")
            file.write("1\n")  # Closed polyline
            
            for x, y in outer_vertices:
                file.write("10\n")
                file.write(f"{x:.3f}\n")
                file.write("20\n")
                file.write(f"{y:.3f}\n")
                
        # Write inner boundary (cutout)
        inner_vertices = vertices.get('inner', [])
        if inner_vertices:
            file.write("0\n")
            file.write("LWPOLYLINE\n")
            file.write("5\n")
            file.write("103\n")  # Handle (simplified)
            file.write("100\n")
            file.write("AcDbEntity\n")
            file.write("8\n")
            file.write(f"{layer}\n")
            file.write("100\n")
            file.write("AcDbPolyline\n")
            file.write("90\n")
            file.write(f"{len(inner_vertices)}\n")
            file.write("70\n")
            file.write("1\n")  # Closed polyline
            
            for x, y in inner_vertices:
                file.write("10\n")
                file.write(f"{x:.3f}\n")
                file.write("20\n")
                file.write(f"{y:.3f}\n")
                
    def _write_dxf_footer(self, file):
        """Write DXF file footer"""
        file.write("0\n")
        file.write("ENDSEC\n")
        file.write("0\n")
        file.write("EOF\n")
        
    def add_text_annotation(self, file, text: str, x: float, y: float, height: float = 10.0, layer: str = "0"):
        """Add text annotation to DXF"""
        file.write("0\n")
        file.write("TEXT\n")
        file.write("5\n")
        file.write("200\n")  # Handle (simplified)
        file.write("100\n")
        file.write("AcDbEntity\n")
        file.write("8\n")
        file.write(f"{layer}\n")
        file.write("100\n")
        file.write("AcDbText\n")
        file.write("10\n")
        file.write(f"{x:.3f}\n")
        file.write("20\n")
        file.write(f"{y:.3f}\n")
        file.write("30\n")
        file.write("0.0\n")
        file.write("40\n")
        file.write(f"{height:.3f}\n")
        file.write("1\n")
        file.write(f"{text}\n")
        
    def add_dimension(self, file, start_point: Tuple[float, float], end_point: Tuple[float, float], 
                     dimension_line_point: Tuple[float, float], text: str = "", layer: str = "0"):
        """Add dimension to DXF (simplified)"""
        # For now, just add lines and text
        # In a full implementation, you would use proper DXF dimension entities
        
        # Dimension line
        file.write("0\n")
        file.write("LINE\n")
        file.write("5\n")
        file.write("300\n")  # Handle (simplified)
        file.write("100\n")
        file.write("AcDbEntity\n")
        file.write("8\n")
        file.write(f"{layer}\n")
        file.write("100\n")
        file.write("AcDbLine\n")
        file.write("10\n")
        file.write(f"{start_point[0]:.3f}\n")
        file.write("20\n")
        file.write(f"{start_point[1]:.3f}\n")
        file.write("30\n")
        file.write("0.0\n")
        file.write("11\n")
        file.write(f"{end_point[0]:.3f}\n")
        file.write("21\n")
        file.write(f"{end_point[1]:.3f}\n")
        file.write("31\n")
        file.write("0.0\n")
        
        # Dimension text
        if text:
            mid_x = (start_point[0] + end_point[0]) / 2
            mid_y = (start_point[1] + end_point[1]) / 2
            self.add_text_annotation(file, text, mid_x, mid_y, 5.0, layer)
