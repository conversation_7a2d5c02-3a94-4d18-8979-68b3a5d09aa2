# Stair Manufacturing Desktop Application

A PySide6-based desktop application for automating stair design and CNC prep workflow. Designed specifically for stair manufacturing companies to streamline the process from design to production.

## Quick Start

1. **Install dependencies**: `pip install -r requirements.txt`
2. **Run the application**: `python run_stair_designer.py`
3. **Test the setup**: `python test_stair_app.py`

## Features

- **Automated Stair Design**: Calculate optimal rise/run based on building codes
- **Multiple Output Formats**:
  - PDF layout drawings (8.5" x 11" format with company branding)
  - DXF files for CNC stringer cutting
  - G-Code for RichAuto CNC controllers (1/2" compression bit)
  - CSV cut lists with inches and sixteenths
- **Cost Estimation**: Material costs with waste buffer and labor markup
- **Building Code Compliance**: Automatic validation against standard building codes
- **Save/Load Jobs**: Project management with job duplication
- **User-Friendly Interface**: Professional GUI with real-time calculations

## Project Structure

```
├── stair-designer/            # Main application directory
│   ├── main.py               # Main PySide6 GUI application
│   ├── core/                 # Core processing modules
│   │   ├── geometry.py       # Legacy template support
│   │   ├── stair_geometry.py # Stair geometry calculations
│   │   ├── dxf_writer.py     # DXF file generation (ezdxf)
│   │   ├── gcode_writer.py   # G-Code generation (RichAuto)
│   │   ├── pdf_generator.py  # PDF layout drawings (8.5"x11")
│   │   ├── cutlist_generator.py # Cut list generation
│   │   └── cost_estimator.py # Cost estimation with markup
│   ├── templates/            # Template definitions
│   │   ├── straight.json     # Straight layout template
│   │   ├── L.json           # L-shaped layout template
│   │   └── U.json           # U-shaped layout template
│   ├── assets/              # Application assets
│   │   └── logo.png         # Company logo
│   └── output/              # Generated output files
│       ├── myjob_layout.pdf # Example PDF layout drawing
│       ├── myjob_cutlist.csv # Example material cut list
│       ├── myjob_stringer.dxf # Example CNC stringer template
│       └── myjob_stringer.nc # Example G-Code for CNC
├── config/                   # Configuration files
│   └── pricing.json         # Custom material pricing
├── requirements.txt          # Python dependencies
├── run_stair_designer.py    # Application launcher script
├── test_stair_app.py        # Test suite
├── test_structure.py        # Structure validation
└── README.md                # This file
```

## Installation

1. **Clone or download the project**:
   ```bash
   git clone <repository-url>
   cd autocad-desktop
   ```

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:

   **Option A - Using the launcher script (recommended):**
   ```bash
   python run_stair_designer.py
   ```

   **Option B - Direct execution:**
   ```bash
   cd stair-designer
   python main.py
   ```

## Dependencies

### Required
- **PySide6**: GUI framework (Qt6 for Python)
- **Python 3.8+**: Core runtime

### Recommended
- **reportlab**: PDF generation with professional layouts
- **ezdxf**: Advanced DXF file generation
- **numpy**: Mathematical calculations

### Optional
- **openpyxl**: Excel export support

## Usage

### Basic Workflow

1. **Launch the application**:
   ```bash
   cd stair-designer
   python main.py
   ```

2. **Enter job information**:
   - Job name and customer details
   - Date and project notes

3. **Input stair parameters**:
   - Total rise (inches)
   - Stair width
   - Tread thickness (defaults to 1")
   - Riser type (Open/Closed/Mixed)
   - Nosing (default ON)
   - Material type (defaults to LSL)

4. **Add landing details** (if applicable):
   - Landing height and dimensions
   - Framing layout option

5. **Calculate geometry**:
   - Click "Calculate Stair Geometry"
   - Review calculations and code compliance
   - Check cut list and cost estimate

6. **Generate outputs**:
   - **PDF Layout**: Professional drawing with company branding
   - **DXF File**: CNC-ready stringer template
   - **G-Code**: RichAuto controller compatible
   - **Cut List**: CSV with material specifications

7. **Save project** for future reference or modifications

## Template System

Templates are defined in JSON format with the following structure:

```json
{
  "name": "Template Name",
  "description": "Template description",
  "type": "template_type",
  "parameters": {
    "parameter_name": {
      "type": "float",
      "default": 1000.0,
      "min": 100.0,
      "max": 10000.0,
      "unit": "mm",
      "description": "Parameter description"
    }
  },
  "geometry": {
    "element_name": {
      "type": "rectangle",
      "width": "{{width}}",
      "height": "{{length}}"
    }
  },
  "cut_list": [
    {
      "name": "Part Name",
      "material": "Material Type",
      "thickness": "{{thickness}}",
      "width": "{{width}}",
      "length": "{{length}}",
      "quantity": 1
    }
  ]
}
```

## Output Formats

### PDF Layout (`*_layout.pdf`)
- Technical drawings with dimensions
- Title block with job information
- Scaled to fit on A4 paper
- Separate layers for main panels and stringers

### DXF Files (`*_stringer.dxf`)
- AutoCAD-compatible format
- Separate layers for different components
- Precise geometry for CNC machining
- Compatible with most CAD software

### G-Code (`*_stringer.nc`)
- CNC router compatible
- Tool compensation included
- Configurable cutting parameters
- Safety features (safe heights, spindle control)

### Cut Lists (`*_cutlist.csv`)
- Material specifications
- Quantities and dimensions
- Area calculations
- Material summary by type

## Customization

### Adding New Templates
1. Create a new JSON file in the `templates/` directory
2. Define parameters, geometry, and cut list
3. Restart the application to load the new template

### Modifying Cutting Parameters
Edit the parameters in `core/gcode_writer.py`:
- `feed_rate`: Cutting speed (mm/min)
- `spindle_speed`: Spindle RPM
- `plunge_rate`: Z-axis feed rate (mm/min)
- `cut_depth`: Cutting depth (mm)
- `tool_diameter`: Tool diameter for compensation (mm)

### Customizing PDF Output
Modify `core/pdf_generator.py` to change:
- Page size and margins
- Title block layout
- Dimension styles
- Color schemes

## Development

### Running Tests
```bash
pytest tests/
```

### Code Formatting
```bash
black .
flake8 .
```

### Adding New Features
1. Core logic goes in the `core/` directory
2. GUI components are added to `main.py`
3. Templates are defined in `templates/`
4. Update this README with new features

## Troubleshooting

### Common Issues

1. **"No module named 'PyQt5'"**:
   ```bash
   pip install PyQt5
   ```

2. **PDF generation not working**:
   ```bash
   pip install reportlab
   ```

3. **Excel export not available**:
   ```bash
   pip install openpyxl
   ```

4. **Output files not generated**:
   - Check that the `output/` directory exists
   - Verify file permissions
   - Check the application status bar for error messages

### Getting Help
- Check the application status bar for error messages
- Review the console output for detailed error information
- Ensure all required dependencies are installed

## License

This project is provided as-is for educational and development purposes.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Future Enhancements

- [ ] 3D visualization
- [ ] More template types
- [ ] Advanced G-Code optimization
- [ ] Material database integration
- [ ] Cost estimation
- [ ] Project management features
- [ ] Cloud storage integration