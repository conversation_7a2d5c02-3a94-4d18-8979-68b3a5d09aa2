# AutoCAD Desktop - Parametric Layout Generator

A PyQt-based desktop application for generating parametric layouts, DXF files, G-code, PDFs, and cut lists for woodworking and fabrication projects.

## Features

- **Parametric Templates**: Support for Straight, L-Shape, and U-Shape layouts
- **Multiple Output Formats**:
  - PDF layout drawings with dimensions
  - DXF files for CAD software and CNC machines
  - G-Code for CNC routers
  - CSV cut lists for material planning
- **User-Friendly GUI**: Tabbed interface with parameter input and preview
- **Customizable Parameters**: Adjustable dimensions, spacing, and material properties
- **Template System**: JSON-based template definitions for easy customization

## Project Structure

```
├── main.py                 # Main PyQt GUI application
├── templates/              # Template definitions
│   ├── straight.json      # Straight layout template
│   ├── L.json             # L-shaped layout template
│   └── U.json             # U-shaped layout template
├── core/                   # Core processing modules
│   ├── geometry.py        # Parametric layout logic
│   ├── dxf_writer.py      # DXF file generation
│   ├── gcode_writer.py    # G-Code generation
│   ├── pdf_generator.py   # PDF layout drawings
│   └── cutlist_generator.py # Cut list generation
├── assets/                 # Application assets
│   └── logo.png           # Application logo
├── output/                 # Generated output files
│   ├── myjob_layout.pdf   # Example PDF output
│   ├── myjob_cutlist.csv  # Example cut list
│   ├── myjob_stringer.dxf # Example DXF output
│   └── myjob_stringer.nc  # Example G-Code output
├── requirements.txt        # Python dependencies
└── README.md              # This file
```

## Installation

1. **Clone or download the project**:
   ```bash
   git clone <repository-url>
   cd autocad-desktop
   ```

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python main.py
   ```

## Dependencies

### Required
- **PyQt5**: GUI framework
- **Python 3.7+**: Core runtime

### Optional (for enhanced features)
- **reportlab**: PDF generation with graphics
- **openpyxl**: Excel export support

## Usage

1. **Launch the application**:
   ```bash
   python main.py
   ```

2. **Select a template** (Straight, L-Shape, or U-Shape)

3. **Enter job name** and adjust parameters:
   - Dimensions (length, width, thickness)
   - Stringer spacing and width
   - Edge margins
   - Corner radius (for L and U shapes)

4. **Generate outputs**:
   - Click "Generate PDF Layout" for technical drawings
   - Click "Generate DXF File" for CAD files
   - Click "Generate G-Code" for CNC machining
   - Click "Generate Cut List" for material planning

5. **Find outputs** in the `output/` directory

## Template System

Templates are defined in JSON format with the following structure:

```json
{
  "name": "Template Name",
  "description": "Template description",
  "type": "template_type",
  "parameters": {
    "parameter_name": {
      "type": "float",
      "default": 1000.0,
      "min": 100.0,
      "max": 10000.0,
      "unit": "mm",
      "description": "Parameter description"
    }
  },
  "geometry": {
    "element_name": {
      "type": "rectangle",
      "width": "{{width}}",
      "height": "{{length}}"
    }
  },
  "cut_list": [
    {
      "name": "Part Name",
      "material": "Material Type",
      "thickness": "{{thickness}}",
      "width": "{{width}}",
      "length": "{{length}}",
      "quantity": 1
    }
  ]
}
```

## Output Formats

### PDF Layout (`*_layout.pdf`)
- Technical drawings with dimensions
- Title block with job information
- Scaled to fit on A4 paper
- Separate layers for main panels and stringers

### DXF Files (`*_stringer.dxf`)
- AutoCAD-compatible format
- Separate layers for different components
- Precise geometry for CNC machining
- Compatible with most CAD software

### G-Code (`*_stringer.nc`)
- CNC router compatible
- Tool compensation included
- Configurable cutting parameters
- Safety features (safe heights, spindle control)

### Cut Lists (`*_cutlist.csv`)
- Material specifications
- Quantities and dimensions
- Area calculations
- Material summary by type

## Customization

### Adding New Templates
1. Create a new JSON file in the `templates/` directory
2. Define parameters, geometry, and cut list
3. Restart the application to load the new template

### Modifying Cutting Parameters
Edit the parameters in `core/gcode_writer.py`:
- `feed_rate`: Cutting speed (mm/min)
- `spindle_speed`: Spindle RPM
- `plunge_rate`: Z-axis feed rate (mm/min)
- `cut_depth`: Cutting depth (mm)
- `tool_diameter`: Tool diameter for compensation (mm)

### Customizing PDF Output
Modify `core/pdf_generator.py` to change:
- Page size and margins
- Title block layout
- Dimension styles
- Color schemes

## Development

### Running Tests
```bash
pytest tests/
```

### Code Formatting
```bash
black .
flake8 .
```

### Adding New Features
1. Core logic goes in the `core/` directory
2. GUI components are added to `main.py`
3. Templates are defined in `templates/`
4. Update this README with new features

## Troubleshooting

### Common Issues

1. **"No module named 'PyQt5'"**:
   ```bash
   pip install PyQt5
   ```

2. **PDF generation not working**:
   ```bash
   pip install reportlab
   ```

3. **Excel export not available**:
   ```bash
   pip install openpyxl
   ```

4. **Output files not generated**:
   - Check that the `output/` directory exists
   - Verify file permissions
   - Check the application status bar for error messages

### Getting Help
- Check the application status bar for error messages
- Review the console output for detailed error information
- Ensure all required dependencies are installed

## License

This project is provided as-is for educational and development purposes.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Future Enhancements

- [ ] 3D visualization
- [ ] More template types
- [ ] Advanced G-Code optimization
- [ ] Material database integration
- [ ] Cost estimation
- [ ] Project management features
- [ ] Cloud storage integration