@echo off
echo Stair Designer Application Builder
echo ===================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo Python found, starting build process...
echo.

REM Run the build script
python build_app.py

echo.
echo Build process completed.
pause
