#!/usr/bin/env python3
"""
Build script for Stair Designer Application
Creates a standalone executable using PyInstaller
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """Check if required build dependencies are installed"""
    print("Checking build dependencies...")
    
    try:
        import PyInstaller
        print("✓ PyInstaller found")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller installed")
    
    # Check if main dependencies are available
    required_packages = ["PySide6", "reportlab", "ezdxf", "numpy"]
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace("-", "_"))
            print(f"✓ {package} found")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} not found")
    
    if missing_packages:
        print(f"\nInstalling missing packages: {', '.join(missing_packages)}")
        subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing_packages)
        print("✓ All packages installed")

def create_spec_file():
    """Create PyInstaller spec file for the application"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['stair-designer/main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('stair-designer/templates', 'templates'),
        ('stair-designer/assets', 'assets'),
        ('stair-designer/config', 'config'),
    ],
    hiddenimports=[
        'PySide6.QtCore',
        'PySide6.QtGui', 
        'PySide6.QtWidgets',
        'reportlab.pdfgen.canvas',
        'reportlab.lib.pagesizes',
        'reportlab.lib.units',
        'reportlab.lib.colors',
        'ezdxf',
        'numpy',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='StairDesigner',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='stair-designer/assets/icon.png' if os.path.exists('stair-designer/assets/icon.png') else None,
)
'''
    
    with open('StairDesigner.spec', 'w') as f:
        f.write(spec_content)
    
    print("✓ Created PyInstaller spec file")

def build_executable():
    """Build the executable using PyInstaller"""
    print("Building executable...")
    
    try:
        # Run PyInstaller
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "StairDesigner.spec"]
        subprocess.check_call(cmd)
        print("✓ Executable built successfully")
        
        # Check if executable was created
        exe_path = "dist/StairDesigner.exe" if os.name == 'nt' else "dist/StairDesigner"
        if os.path.exists(exe_path):
            print(f"✓ Executable created: {exe_path}")
            return True
        else:
            print("❌ Executable not found after build")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        return False

def create_installer_files():
    """Create additional files for distribution"""
    print("Creating distribution files...")
    
    # Create output directory in dist
    dist_output = "dist/output"
    os.makedirs(dist_output, exist_ok=True)
    
    # Copy sample files
    if os.path.exists("stair-designer/output"):
        for file in os.listdir("stair-designer/output"):
            if file.endswith(('.pdf', '.csv', '.dxf', '.nc')):
                shutil.copy2(
                    os.path.join("stair-designer/output", file),
                    os.path.join(dist_output, file)
                )
    
    # Create README for distribution
    readme_content = """# Stair Designer Application

## Quick Start
1. Run StairDesigner.exe
2. Enter your stair parameters
3. Click "Calculate Stair Geometry"
4. Generate your output files

## Output Files
Generated files will be saved in the 'output' folder:
- PDF layouts for printing
- DXF files for CNC machines
- G-Code for RichAuto controllers
- CSV cut lists for material planning

## Support
For support and updates, contact your software provider.
"""
    
    with open("dist/README.txt", "w") as f:
        f.write(readme_content)
    
    print("✓ Distribution files created")

def cleanup():
    """Clean up build artifacts"""
    print("Cleaning up...")
    
    # Remove build artifacts
    if os.path.exists("build"):
        shutil.rmtree("build")
    
    if os.path.exists("StairDesigner.spec"):
        os.remove("StairDesigner.spec")
    
    print("✓ Cleanup complete")

def main():
    """Main build process"""
    print("Stair Designer Application Builder")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("stair-designer/main.py"):
        print("❌ Error: main.py not found in stair-designer directory")
        print("Please run this script from the project root directory")
        return 1
    
    try:
        # Build process
        check_dependencies()
        create_spec_file()
        
        if build_executable():
            create_installer_files()
            print("\n" + "=" * 50)
            print("🎉 Build completed successfully!")
            print("\nExecutable location:")
            exe_path = "dist/StairDesigner.exe" if os.name == 'nt' else "dist/StairDesigner"
            print(f"  {os.path.abspath(exe_path)}")
            print("\nDistribution folder:")
            print(f"  {os.path.abspath('dist')}")
            
            # Ask about cleanup
            response = input("\nClean up build files? (y/n): ").lower().strip()
            if response in ['y', 'yes']:
                cleanup()
            
            return 0
        else:
            print("\n❌ Build failed")
            return 1
            
    except KeyboardInterrupt:
        print("\n\nBuild cancelled by user")
        return 1
    except Exception as e:
        print(f"\n❌ Build error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
