"""
Parametric Layout Logic
Handles geometric calculations and layout generation based on templates
"""

import json
import math
import re
from typing import Dict, List, Any, Tuple


class ParametricLayout:
    """Main class for handling parametric layout generation"""
    
    def __init__(self):
        self.templates = {}
        self.load_templates()
        
    def load_templates(self):
        """Load all template files"""
        import os
        template_dir = "templates"
        
        if not os.path.exists(template_dir):
            return
            
        for filename in os.listdir(template_dir):
            if filename.endswith('.json'):
                template_name = filename[:-5]  # Remove .json extension
                try:
                    with open(os.path.join(template_dir, filename), 'r') as f:
                        self.templates[template_name] = json.load(f)
                except Exception as e:
                    print(f"Error loading template {filename}: {e}")
                    
    def get_template(self, template_name: str) -> Dict:
        """Get a specific template by name"""
        return self.templates.get(template_name, {})
        
    def evaluate_expression(self, expression: str, parameters: Dict) -> float:
        """Safely evaluate mathematical expressions with parameters"""
        if not isinstance(expression, str):
            return float(expression)
            
        # Replace parameter placeholders
        for param, value in parameters.items():
            expression = expression.replace(f"{{{{{param}}}}}", str(value))
            
        # Handle mathematical expressions
        try:
            # Simple expression evaluation (extend as needed)
            expression = expression.replace('pi', str(math.pi))
            expression = expression.replace('PI', str(math.pi))
            
            # Basic safety check - only allow numbers, operators, and math functions
            allowed_chars = set('0123456789+-*/().() ')
            allowed_funcs = ['sin', 'cos', 'tan', 'sqrt', 'abs', 'min', 'max']
            
            # For now, use eval with caution (in production, use a proper expression parser)
            result = eval(expression)
            return float(result)
        except:
            return 0.0
            
    def generate_geometry(self, template_name: str, parameters: Dict) -> Dict:
        """Generate geometry based on template and parameters"""
        template = self.get_template(template_name)
        if not template:
            raise ValueError(f"Template '{template_name}' not found")
            
        geometry = template.get('geometry', {})
        generated_geometry = {}
        
        for element_name, element_def in geometry.items():
            generated_geometry[element_name] = self._process_geometry_element(element_def, parameters)
            
        return generated_geometry
        
    def _process_geometry_element(self, element_def: Dict, parameters: Dict) -> Dict:
        """Process a single geometry element"""
        element_type = element_def.get('type', 'rectangle')
        
        if element_type == 'rectangle':
            return self._process_rectangle(element_def, parameters)
        elif element_type == 'l_shape':
            return self._process_l_shape(element_def, parameters)
        elif element_type == 'u_shape':
            return self._process_u_shape(element_def, parameters)
        elif element_type == 'array':
            return self._process_array(element_def, parameters)
        else:
            return element_def
            
    def _process_rectangle(self, element_def: Dict, parameters: Dict) -> Dict:
        """Process a rectangle geometry element"""
        width = self.evaluate_expression(element_def.get('width', '0'), parameters)
        height = self.evaluate_expression(element_def.get('height', '0'), parameters)
        
        position = element_def.get('position', {'x': 0, 'y': 0})
        x = self.evaluate_expression(position.get('x', '0'), parameters)
        y = self.evaluate_expression(position.get('y', '0'), parameters)
        
        rotation = self.evaluate_expression(element_def.get('rotation', '0'), parameters)
        
        return {
            'type': 'rectangle',
            'width': width,
            'height': height,
            'position': {'x': x, 'y': y},
            'rotation': rotation,
            'vertices': self._calculate_rectangle_vertices(width, height, x, y, rotation)
        }
        
    def _process_l_shape(self, element_def: Dict, parameters: Dict) -> Dict:
        """Process an L-shape geometry element"""
        leg1 = element_def.get('leg1', {})
        leg2 = element_def.get('leg2', {})
        corner_radius = self.evaluate_expression(element_def.get('corner_radius', '0'), parameters)
        
        leg1_width = self.evaluate_expression(leg1.get('width', '0'), parameters)
        leg1_height = self.evaluate_expression(leg1.get('height', '0'), parameters)
        leg2_width = self.evaluate_expression(leg2.get('width', '0'), parameters)
        leg2_height = self.evaluate_expression(leg2.get('height', '0'), parameters)
        
        return {
            'type': 'l_shape',
            'leg1': {'width': leg1_width, 'height': leg1_height},
            'leg2': {'width': leg2_width, 'height': leg2_height},
            'corner_radius': corner_radius,
            'vertices': self._calculate_l_shape_vertices(leg1_width, leg1_height, leg2_width, leg2_height, corner_radius)
        }
        
    def _process_u_shape(self, element_def: Dict, parameters: Dict) -> Dict:
        """Process a U-shape geometry element"""
        outer = element_def.get('outer', {})
        inner = element_def.get('inner', {})
        corner_radius = self.evaluate_expression(element_def.get('corner_radius', '0'), parameters)
        
        outer_width = self.evaluate_expression(outer.get('width', '0'), parameters)
        outer_height = self.evaluate_expression(outer.get('height', '0'), parameters)
        inner_width = self.evaluate_expression(inner.get('width', '0'), parameters)
        inner_height = self.evaluate_expression(inner.get('height', '0'), parameters)
        
        inner_position = inner.get('position', {'x': 0, 'y': 0})
        inner_x = self.evaluate_expression(inner_position.get('x', '0'), parameters)
        inner_y = self.evaluate_expression(inner_position.get('y', '0'), parameters)
        
        return {
            'type': 'u_shape',
            'outer': {'width': outer_width, 'height': outer_height},
            'inner': {'width': inner_width, 'height': inner_height, 'position': {'x': inner_x, 'y': inner_y}},
            'corner_radius': corner_radius,
            'vertices': self._calculate_u_shape_vertices(outer_width, outer_height, inner_width, inner_height, inner_x, inner_y, corner_radius)
        }
        
    def _process_array(self, element_def: Dict, parameters: Dict) -> Dict:
        """Process an array of geometry elements"""
        count = int(self.evaluate_expression(element_def.get('count', '1'), parameters))
        element_template = element_def.get('element', {})
        pattern = element_def.get('pattern', 'linear')
        
        elements = []
        for i in range(count):
            # Add index to parameters for element processing
            element_params = parameters.copy()
            element_params['index'] = i
            
            element = self._process_geometry_element(element_template, element_params)
            elements.append(element)
            
        return {
            'type': 'array',
            'pattern': pattern,
            'count': count,
            'elements': elements
        }
        
    def _calculate_rectangle_vertices(self, width: float, height: float, x: float, y: float, rotation: float) -> List[Tuple[float, float]]:
        """Calculate vertices for a rectangle"""
        # Basic rectangle vertices (before rotation)
        vertices = [
            (0, 0),
            (width, 0),
            (width, height),
            (0, height)
        ]
        
        # Apply rotation if needed
        if rotation != 0:
            cos_r = math.cos(math.radians(rotation))
            sin_r = math.sin(math.radians(rotation))
            
            rotated_vertices = []
            for vx, vy in vertices:
                new_x = vx * cos_r - vy * sin_r
                new_y = vx * sin_r + vy * cos_r
                rotated_vertices.append((new_x, new_y))
            vertices = rotated_vertices
            
        # Apply translation
        return [(vx + x, vy + y) for vx, vy in vertices]
        
    def _calculate_l_shape_vertices(self, leg1_width: float, leg1_height: float, leg2_width: float, leg2_height: float, corner_radius: float) -> List[Tuple[float, float]]:
        """Calculate vertices for an L-shape"""
        # Simplified L-shape vertices (without corner radius for now)
        vertices = [
            (0, 0),
            (leg1_width, 0),
            (leg1_width, leg1_height - leg2_height),
            (leg1_width + leg2_width - leg1_width, leg1_height - leg2_height),
            (leg2_width, leg1_height),
            (0, leg1_height)
        ]
        
        return vertices
        
    def _calculate_u_shape_vertices(self, outer_width: float, outer_height: float, inner_width: float, inner_height: float, inner_x: float, inner_y: float, corner_radius: float) -> List[Tuple[float, float]]:
        """Calculate vertices for a U-shape"""
        # Outer rectangle vertices
        outer_vertices = [
            (0, 0),
            (outer_width, 0),
            (outer_width, outer_height),
            (0, outer_height)
        ]
        
        # Inner rectangle vertices (cutout)
        inner_vertices = [
            (inner_x, inner_y),
            (inner_x + inner_width, inner_y),
            (inner_x + inner_width, inner_y + inner_height),
            (inner_x, inner_y + inner_height)
        ]
        
        return {
            'outer': outer_vertices,
            'inner': inner_vertices
        }
        
    def calculate_area(self, geometry: Dict) -> float:
        """Calculate the total area of a geometry"""
        geom_type = geometry.get('type', 'rectangle')
        
        if geom_type == 'rectangle':
            return geometry['width'] * geometry['height']
        elif geom_type == 'l_shape':
            leg1 = geometry['leg1']
            leg2 = geometry['leg2']
            # Simplified area calculation
            return leg1['width'] * leg1['height'] + leg2['width'] * leg2['height'] - leg1['width'] * leg2['height']
        elif geom_type == 'u_shape':
            outer = geometry['outer']
            inner = geometry['inner']
            return outer['width'] * outer['height'] - inner['width'] * inner['height']
        elif geom_type == 'array':
            total_area = 0
            for element in geometry['elements']:
                total_area += self.calculate_area(element)
            return total_area
        else:
            return 0.0
            
    def get_bounding_box(self, geometry: Dict) -> Dict:
        """Get the bounding box of a geometry"""
        vertices = geometry.get('vertices', [])
        if not vertices:
            return {'min_x': 0, 'min_y': 0, 'max_x': 0, 'max_y': 0}
            
        if isinstance(vertices, dict):  # U-shape case
            all_vertices = vertices.get('outer', []) + vertices.get('inner', [])
        else:
            all_vertices = vertices
            
        if not all_vertices:
            return {'min_x': 0, 'min_y': 0, 'max_x': 0, 'max_y': 0}
            
        x_coords = [v[0] for v in all_vertices]
        y_coords = [v[1] for v in all_vertices]
        
        return {
            'min_x': min(x_coords),
            'min_y': min(y_coords),
            'max_x': max(x_coords),
            'max_y': max(y_coords)
        }
