#!/usr/bin/env python3
"""
Test script for the Stair Manufacturing Desktop Application
Tests core functionality without GUI
"""

import os
import sys

def test_stair_geometry():
    """Test stair geometry calculations"""
    print("Testing stair geometry calculations...")
    
    try:
        from core.stair_geometry import StairGeometry
        
        stair_geom = StairGeometry()
        
        # Test parameters
        test_params = {
            'job_name': 'Test_Stair',
            'total_rise': 108.0,  # 9 feet
            'stair_width': 36.0,
            'tread_thickness': '1',
            'riser_type': 'Open',
            'has_nosing': True,
            'material': 'LSL',
            'landing_height': 0,
            'landing_width': 36,
            'landing_depth': 36
        }
        
        # Calculate stair
        results = stair_geom.calculate_stair(test_params)
        
        if results.get('calculations_valid'):
            print("✓ Stair geometry calculation successful")
            
            rise_run = results['rise_run']
            print(f"  - Steps: {rise_run['num_treads']}")
            print(f"  - Rise: {rise_run['riser_height']:.3f}\"")
            print(f"  - Run: {rise_run['tread_depth']:.3f}\"")
            
            # Test cut list generation
            cut_list = stair_geom.get_cut_list()
            print(f"  - Cut list items: {len(cut_list)}")
            
            return True
        else:
            print("❌ Stair geometry calculation failed")
            return False
            
    except Exception as e:
        print(f"❌ Stair geometry test failed: {e}")
        return False

def test_cost_estimation():
    """Test cost estimation"""
    print("\nTesting cost estimation...")
    
    try:
        from core.stair_geometry import StairGeometry
        from core.cost_estimator import CostEstimator
        
        stair_geom = StairGeometry()
        cost_estimator = CostEstimator()
        
        # Test parameters
        test_params = {
            'job_name': 'Test_Stair',
            'total_rise': 108.0,
            'stair_width': 36.0,
            'tread_thickness': '1',
            'riser_type': 'Open',
            'has_nosing': True,
            'material': 'LSL'
        }
        
        # Calculate stair
        results = stair_geom.calculate_stair(test_params)
        
        if results.get('calculations_valid'):
            cut_list = stair_geom.get_cut_list()
            cost_data = cost_estimator.calculate_project_cost(results, cut_list)
            
            if 'total_cost' in cost_data:
                print("✓ Cost estimation successful")
                print(f"  - Total cost: ${cost_data['total_cost']:.2f}")
                print(f"  - Cost per step: ${cost_data['cost_per_step']:.2f}")
                return True
            else:
                print("❌ Cost estimation failed")
                return False
        else:
            print("❌ Cost estimation failed - invalid stair geometry")
            return False
            
    except Exception as e:
        print(f"❌ Cost estimation test failed: {e}")
        return False

def test_file_generation():
    """Test file generation capabilities"""
    print("\nTesting file generation...")
    
    try:
        from core.stair_geometry import StairGeometry
        from core.dxf_writer import DXFWriter
        from core.gcode_writer import GCodeWriter
        from core.pdf_generator import PDFGenerator
        from core.cutlist_generator import CutlistGenerator
        
        # Create test directories
        os.makedirs("output", exist_ok=True)
        
        stair_geom = StairGeometry()
        
        # Test parameters
        test_params = {
            'job_name': 'Test_Stair',
            'total_rise': 108.0,
            'stair_width': 36.0,
            'tread_thickness': '1',
            'riser_type': 'Open',
            'has_nosing': True,
            'material': 'LSL'
        }
        
        # Calculate stair
        results = stair_geom.calculate_stair(test_params)
        
        if not results.get('calculations_valid'):
            print("❌ File generation test failed - invalid stair geometry")
            return False
            
        success_count = 0
        
        # Test DXF generation
        try:
            dxf_writer = DXFWriter()
            dxf_writer.write_stair_dxf(results, "output/test_stair.dxf")
            print("✓ DXF generation successful")
            success_count += 1
        except Exception as e:
            print(f"❌ DXF generation failed: {e}")

        # Test G-code generation
        try:
            gcode_writer = GCodeWriter()
            gcode_writer.write_stair_gcode(results, "output/test_stair.nc")
            print("✓ G-code generation successful")
            success_count += 1
        except Exception as e:
            print(f"❌ G-code generation failed: {e}")

        # Test PDF generation
        try:
            pdf_generator = PDFGenerator()
            pdf_generator.generate_stair_pdf(results, "output/test_stair.pdf")
            print("✓ PDF generation successful")
            success_count += 1
        except Exception as e:
            print(f"❌ PDF generation failed: {e}")

        # Test cut list generation
        try:
            cutlist_generator = CutlistGenerator()
            cut_list = stair_geom.get_cut_list()
            cutlist_generator.generate_stair_cutlist(results, cut_list, "output/test_cutlist.csv")
            print("✓ Cut list generation successful")
            success_count += 1
        except Exception as e:
            print(f"❌ Cut list generation failed: {e}")
            
        return success_count >= 3  # At least 3 out of 4 should work
        
    except Exception as e:
        print(f"❌ File generation test failed: {e}")
        return False

def test_gui_imports():
    """Test that GUI components can be imported"""
    print("\nTesting GUI imports...")
    
    try:
        # Test PySide6 import
        try:
            from PySide6.QtWidgets import QApplication
            print("✓ PySide6 available")
            pyside_available = True
        except ImportError:
            print("❌ PySide6 not available - install with: pip install PySide6")
            pyside_available = False
            
        # Test main window import
        try:
            from main import MainWindow
            print("✓ Main window class imports successfully")
            main_available = True
        except ImportError as e:
            print(f"❌ Main window import failed: {e}")
            main_available = False
            
        return pyside_available and main_available
        
    except Exception as e:
        print(f"❌ GUI import test failed: {e}")
        return False

def write_test_results(results: dict):
    """Write test results to output folder"""
    os.makedirs("output", exist_ok=True)

    # Write test results to a file
    with open("output/test_results.txt", "w") as f:
        f.write("STAIR MANUFACTURING APPLICATION - TEST RESULTS\n")
        f.write("=" * 60 + "\n\n")

        f.write(f"Test Date: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Tests: {results['total']}\n")
        f.write(f"Passed: {results['passed']}\n")
        f.write(f"Failed: {results['failed']}\n")
        f.write(f"Success Rate: {(results['passed']/results['total']*100):.1f}%\n\n")

        f.write("TEST DETAILS:\n")
        f.write("-" * 30 + "\n")
        for test_name, status in results['details'].items():
            status_symbol = "✓" if status else "❌"
            f.write(f"{status_symbol} {test_name}\n")

        f.write("\nGenerated test files in output/ directory:\n")
        f.write("- test_stair.dxf (DXF stringer template)\n")
        f.write("- test_stair.nc (G-code for CNC)\n")
        f.write("- test_stair.pdf (PDF layout)\n")
        f.write("- test_cutlist.csv (Material cut list)\n")
        f.write("- test_results.txt (This file)\n")

def main():
    """Run all tests"""
    print("Stair Manufacturing Desktop Application - Test Suite")
    print("=" * 60)
    
    tests = [
        ("Stair Geometry", test_stair_geometry),
        ("Cost Estimation", test_cost_estimation),
        ("File Generation", test_file_generation),
        ("GUI Imports", test_gui_imports)
    ]

    passed = 0
    total = len(tests)
    test_details = {}

    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 40)

        test_result = test_func()
        test_details[test_name] = test_result

        if test_result:
            passed += 1
            
    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")

    # Write test results to output folder
    results_data = {
        'total': total,
        'passed': passed,
        'failed': total - passed,
        'details': test_details
    }

    try:
        write_test_results(results_data)
        print(f"\n📄 Test results written to: output/test_results.txt")
    except Exception as e:
        print(f"\n⚠ Could not write test results: {e}")

    if passed == total:
        print("🎉 All tests passed! The application is ready to use.")
        print("\nTo run the GUI application:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Run the application: python main.py")
        print("\n📁 Generated test files are available in the output/ directory")
    else:
        print("⚠ Some tests failed. Check the issues above.")
        print("\nTo install missing dependencies:")
        print("pip install -r requirements.txt")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
