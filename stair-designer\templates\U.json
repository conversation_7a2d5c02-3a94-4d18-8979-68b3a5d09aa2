{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "U-shaped stair with two landings and three flight segments", "type": "u_shaped_stair", "category": "stairs", "parameters": {"total_rise": {"type": "float", "default": 108.0, "min": 84.0, "max": 144.0, "unit": "inches", "description": "Total vertical rise of the stair"}, "stair_width": {"type": "float", "default": 36.0, "min": 30.0, "max": 48.0, "unit": "inches", "description": "Width of all stair flights"}, "well_width": {"type": "float", "default": 48.0, "min": 36.0, "max": 72.0, "unit": "inches", "description": "Width of the stair well opening"}, "well_length": {"type": "float", "default": 120.0, "min": 96.0, "max": 180.0, "unit": "inches", "description": "Length of the stair well opening"}, "landing_size": {"type": "float", "default": 36.0, "min": 30.0, "max": 60.0, "unit": "inches", "description": "Size of intermediate and top landings"}, "first_flight_steps": {"type": "integer", "default": 6, "min": 3, "max": 10, "description": "Number of steps in first flight"}, "second_flight_steps": {"type": "integer", "default": 6, "min": 3, "max": 10, "description": "Number of steps in second flight"}, "tread_thickness": {"type": "string", "default": "1\"", "options": ["3/4\"", "1\"", "1-1/4\"", "1-1/2\"", "2\""], "description": "Thickness of tread material"}, "material": {"type": "string", "default": "LSL", "options": ["LSL", "LVL", "Solid Wood", "Plywood"], "description": "Stringer material type"}, "has_nosing": {"type": "boolean", "default": true, "description": "Include nosing on treads"}, "riser_type": {"type": "string", "default": "Open", "options": ["Open", "Closed", "Mixed"], "description": "Type of riser configuration"}}, "calculations": {"total_input_steps": "first_flight_steps + second_flight_steps", "estimated_steps": "total_input_steps + 6", "initial_riser": "total_rise / estimated_steps", "adjusted_riser": "max(7.0, min(7.75, initial_riser))", "final_steps": "round(total_rise / adjusted_riser)", "third_flight_steps": "final_steps - first_flight_steps - second_flight_steps", "optimal_riser_height": "total_rise / final_steps", "calculated_tread_depth": "25 - (2 * optimal_riser_height)", "tread_depth": "max(10.0, min(11.0, calculated_tread_depth))", "tread_depth_with_nosing": "tread_depth + 1", "first_flight_run": "tread_depth * (first_flight_steps - 1)", "second_flight_run": "tread_depth * (second_flight_steps - 1)", "third_flight_run": "tread_depth * (third_flight_steps - 1)", "first_flight_rise": "optimal_riser_height * first_flight_steps", "second_flight_rise": "optimal_riser_height * second_flight_steps", "third_flight_rise": "optimal_riser_height * third_flight_steps", "first_stringer_length": "sqrt(first_flight_run * first_flight_run + first_flight_rise * first_flight_rise)", "second_stringer_length": "sqrt(second_flight_run * second_flight_run + second_flight_rise * second_flight_rise)", "third_stringer_length": "sqrt(third_flight_run * third_flight_run + third_flight_rise * third_flight_rise)", "stringer_count_per_flight": "stair_width <= 36 ? 3 : ceil(stair_width / 16)"}, "geometry": {"first_flight_stringers": {"type": "array", "count": "{{stringer_count_per_flight}}", "element": {"type": "stringer_profile", "material": "{{material}}", "width": 11.25, "thickness": 1.5, "length": "{{first_stringer_length}}", "flight": "first", "steps": "{{first_flight_steps}}"}}, "second_flight_stringers": {"type": "array", "count": "{{stringer_count_per_flight}}", "element": {"type": "stringer_profile", "material": "{{material}}", "width": 11.25, "thickness": 1.5, "length": "{{second_stringer_length}}", "flight": "second", "steps": "{{second_flight_steps}}"}}, "third_flight_stringers": {"type": "array", "count": "{{stringer_count_per_flight}}", "element": {"type": "stringer_profile", "material": "{{material}}", "width": 11.25, "thickness": 1.5, "length": "{{third_stringer_length}}", "flight": "third", "steps": "{{third_flight_steps}}"}}, "intermediate_landing": {"type": "rectangle", "width": "{{landing_size}}", "depth": "{{landing_size}}", "thickness": "{{tread_thickness}}"}, "top_landing": {"type": "rectangle", "width": "{{landing_size}}", "depth": "{{landing_size}}", "thickness": "{{tread_thickness}}"}}, "cut_list": [{"name": "First Flight Stringers", "material": "{{material}}", "description": "2x12 stringer material for first flight", "thickness": "1-1/2\"", "width": "11-1/4\"", "length": "{{ceil(first_stringer_length / 12) * 12}}'", "quantity": "{{stringer_count_per_flight}}", "notes": "Cut to first flight profile"}, {"name": "Second Flight Stringers", "material": "{{material}}", "description": "2x12 stringer material for second flight", "thickness": "1-1/2\"", "width": "11-1/4\"", "length": "{{ceil(second_stringer_length / 12) * 12}}'", "quantity": "{{stringer_count_per_flight}}", "notes": "Cut to second flight profile"}, {"name": "Third Flight Stringers", "material": "{{material}}", "description": "2x12 stringer material for third flight", "thickness": "1-1/2\"", "width": "11-1/4\"", "length": "{{ceil(third_stringer_length / 12) * 12}}'", "quantity": "{{stringer_count_per_flight}}", "notes": "Cut to third flight profile"}, {"name": "Landing Frames", "material": "Lumber", "description": "2x10 landing frame joists for both landings", "thickness": "1-1/2\"", "width": "9-1/4\"", "length": "{{landing_size}}\"", "quantity": "{{2 * (ceil(landing_size / 16) + 1)}}", "notes": "16\" on center spacing for both landings"}, {"name": "All Flight Treads", "material": "Hardwood", "description": "Tread boards for all three flights", "thickness": "{{tread_thickness}}", "width": "{{stair_width}}\"", "length": "{{tread_depth_with_nosing}}\"", "quantity": "{{(first_flight_steps - 1) + (second_flight_steps - 1) + (third_flight_steps - 1)}}", "notes": "Include nosing profile"}], "building_codes": {"max_riser_height": 7.75, "min_riser_height": 7.0, "max_tread_depth": 11.0, "min_tread_depth": 10.0, "min_landing_size": 30.0, "min_well_width": 36.0, "two_r_plus_t_min": 23.5, "two_r_plus_t_max": 25.5}, "validation_rules": [{"rule": "optimal_riser_height >= 7.0 && optimal_riser_height <= 7.75", "message": "Riser height must be between 7\" and 7.75\""}, {"rule": "tread_depth >= 10.0 && tread_depth <= 11.0", "message": "Tread depth must be between 10\" and 11\""}, {"rule": "(2 * optimal_riser_height + tread_depth) >= 23.5 && (2 * optimal_riser_height + tread_depth) <= 25.5", "message": "2R + T rule: must be between 23.5\" and 25.5\" (currently acceptable range)"}, {"rule": "landing_size >= 30.0", "message": "Landing size must be at least 30\""}, {"rule": "well_width >= 36.0", "message": "Stair well width must be at least 36\""}]}