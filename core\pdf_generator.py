"""
PDF Generator Module
Generates PDF layout drawings from parametric geometry
"""

import os
from typing import Dict, List, Tuple, Any
from .geometry import ParametricLayout


class PDFGenerator:
    """Class for generating PDF layout drawings from parametric geometry"""
    
    def __init__(self):
        self.layout_engine = ParametricLayout()
        self.page_width = 210  # A4 width in mm
        self.page_height = 297  # A4 height in mm
        self.margin = 20  # mm
        self.scale = 1.0
        
    def generate(self, parameters: Dict, output_path: str):
        """Generate PDF layout based on parameters"""
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import A4
            from reportlab.lib.units import mm
            from reportlab.lib.colors import black, red, blue
        except ImportError:
            # Fallback to simple text-based PDF generation
            self._generate_simple_pdf(parameters, output_path)
            return
            
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Get template name from parameters
        template_name = parameters.get('template', 'straight')
        
        # Generate geometry
        geometry = self.layout_engine.generate_geometry(template_name, parameters)
        
        # Calculate scale to fit on page
        self._calculate_scale(geometry)
        
        # Create PDF
        c = canvas.Canvas(output_path, pagesize=A4)
        
        # Draw title block
        self._draw_title_block(c, parameters)
        
        # Draw geometry
        self._draw_geometry(c, geometry)
        
        # Draw dimensions
        self._draw_dimensions(c, geometry, parameters)
        
        # Save PDF
        c.save()
        
    def _generate_simple_pdf(self, parameters: Dict, output_path: str):
        """Generate a simple text-based PDF when reportlab is not available"""
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Get template name from parameters
        template_name = parameters.get('template', 'straight')
        
        # Generate geometry
        geometry = self.layout_engine.generate_geometry(template_name, parameters)
        
        # Create a simple text file with .pdf extension
        with open(output_path, 'w') as f:
            f.write("AutoCAD Desktop - Layout Drawing\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Job Name: {parameters.get('job_name', 'myjob')}\n")
            f.write(f"Template: {template_name}\n")
            f.write(f"Date: {self._get_current_date()}\n\n")
            
            f.write("Parameters:\n")
            f.write("-" * 20 + "\n")
            for key, value in parameters.items():
                if key not in ['template', 'job_name']:
                    f.write(f"{key}: {value}\n")
            f.write("\n")
            
            f.write("Geometry Summary:\n")
            f.write("-" * 20 + "\n")
            for element_name, element in geometry.items():
                f.write(f"{element_name}: {element.get('type', 'unknown')}\n")
                if element.get('type') == 'rectangle':
                    f.write(f"  Size: {element.get('width', 0):.1f} x {element.get('height', 0):.1f} mm\n")
                elif element.get('type') == 'array':
                    f.write(f"  Count: {element.get('count', 0)} elements\n")
            f.write("\n")
            
            f.write("Note: Install 'reportlab' package for full PDF generation with drawings.\n")
            f.write("pip install reportlab\n")
            
    def _calculate_scale(self, geometry: Dict):
        """Calculate scale to fit geometry on page"""
        # Find overall bounding box
        min_x, min_y, max_x, max_y = float('inf'), float('inf'), float('-inf'), float('-inf')
        
        for element in geometry.values():
            bbox = self.layout_engine.get_bounding_box(element)
            min_x = min(min_x, bbox['min_x'])
            min_y = min(min_y, bbox['min_y'])
            max_x = max(max_x, bbox['max_x'])
            max_y = max(max_y, bbox['max_y'])
            
        if min_x == float('inf'):  # No geometry
            self.scale = 1.0
            return
            
        # Calculate required dimensions
        width = max_x - min_x
        height = max_y - min_y
        
        # Available space (accounting for margins and title block)
        available_width = self.page_width - 2 * self.margin
        available_height = self.page_height - 2 * self.margin - 50  # 50mm for title block
        
        # Calculate scale
        scale_x = available_width / width if width > 0 else 1.0
        scale_y = available_height / height if height > 0 else 1.0
        
        self.scale = min(scale_x, scale_y, 1.0)  # Don't scale up
        
    def _draw_title_block(self, canvas, parameters: Dict):
        """Draw title block on PDF"""
        from reportlab.lib.units import mm
        
        # Title block rectangle
        x = self.margin * mm
        y = (self.page_height - self.margin - 40) * mm
        width = (self.page_width - 2 * self.margin) * mm
        height = 35 * mm
        
        canvas.rect(x, y, width, height)
        
        # Title
        canvas.setFont("Helvetica-Bold", 16)
        canvas.drawString(x + 5*mm, y + height - 10*mm, "AutoCAD Desktop - Layout Drawing")
        
        # Job information
        canvas.setFont("Helvetica", 10)
        canvas.drawString(x + 5*mm, y + height - 20*mm, f"Job: {parameters.get('job_name', 'myjob')}")
        canvas.drawString(x + 5*mm, y + height - 28*mm, f"Template: {parameters.get('template', 'straight')}")
        canvas.drawString(x + width - 60*mm, y + height - 20*mm, f"Date: {self._get_current_date()}")
        canvas.drawString(x + width - 60*mm, y + height - 28*mm, f"Scale: 1:{1/self.scale:.0f}")
        
    def _draw_geometry(self, canvas, geometry: Dict):
        """Draw geometry on PDF"""
        from reportlab.lib.units import mm
        from reportlab.lib.colors import black, red
        
        # Drawing area offset
        offset_x = self.margin * mm
        offset_y = self.margin * mm
        
        for element_name, element in geometry.items():
            # Set color based on element type
            if element_name.startswith('stringer'):
                canvas.setStrokeColor(red)
            else:
                canvas.setStrokeColor(black)
                
            self._draw_geometry_element(canvas, element, offset_x, offset_y)
            
    def _draw_geometry_element(self, canvas, element: Dict, offset_x: float, offset_y: float):
        """Draw a single geometry element"""
        from reportlab.lib.units import mm
        
        element_type = element.get('type', 'rectangle')
        
        if element_type == 'rectangle':
            self._draw_rectangle_pdf(canvas, element, offset_x, offset_y)
        elif element_type == 'l_shape':
            self._draw_l_shape_pdf(canvas, element, offset_x, offset_y)
        elif element_type == 'u_shape':
            self._draw_u_shape_pdf(canvas, element, offset_x, offset_y)
        elif element_type == 'array':
            for sub_element in element.get('elements', []):
                self._draw_geometry_element(canvas, sub_element, offset_x, offset_y)
                
    def _draw_rectangle_pdf(self, canvas, element: Dict, offset_x: float, offset_y: float):
        """Draw a rectangle on PDF"""
        from reportlab.lib.units import mm
        
        vertices = element.get('vertices', [])
        if len(vertices) < 3:
            return
            
        # Convert to PDF coordinates
        path = canvas.beginPath()
        first_vertex = True
        
        for x, y in vertices:
            pdf_x = offset_x + x * self.scale * mm
            pdf_y = offset_y + y * self.scale * mm
            
            if first_vertex:
                path.moveTo(pdf_x, pdf_y)
                first_vertex = False
            else:
                path.lineTo(pdf_x, pdf_y)
                
        path.close()
        canvas.drawPath(path)
        
    def _draw_l_shape_pdf(self, canvas, element: Dict, offset_x: float, offset_y: float):
        """Draw an L-shape on PDF"""
        # Same as rectangle for now
        self._draw_rectangle_pdf(canvas, element, offset_x, offset_y)
        
    def _draw_u_shape_pdf(self, canvas, element: Dict, offset_x: float, offset_y: float):
        """Draw a U-shape on PDF"""
        from reportlab.lib.units import mm
        
        vertices = element.get('vertices', {})
        
        # Draw outer boundary
        outer_vertices = vertices.get('outer', [])
        if outer_vertices:
            path = canvas.beginPath()
            first_vertex = True
            
            for x, y in outer_vertices:
                pdf_x = offset_x + x * self.scale * mm
                pdf_y = offset_y + y * self.scale * mm
                
                if first_vertex:
                    path.moveTo(pdf_x, pdf_y)
                    first_vertex = False
                else:
                    path.lineTo(pdf_x, pdf_y)
                    
            path.close()
            canvas.drawPath(path)
            
        # Draw inner boundary
        inner_vertices = vertices.get('inner', [])
        if inner_vertices:
            path = canvas.beginPath()
            first_vertex = True
            
            for x, y in inner_vertices:
                pdf_x = offset_x + x * self.scale * mm
                pdf_y = offset_y + y * self.scale * mm
                
                if first_vertex:
                    path.moveTo(pdf_x, pdf_y)
                    first_vertex = False
                else:
                    path.lineTo(pdf_x, pdf_y)
                    
            path.close()
            canvas.drawPath(path)
            
    def _draw_dimensions(self, canvas, geometry: Dict, parameters: Dict):
        """Draw dimensions on PDF"""
        from reportlab.lib.units import mm
        
        # For now, just add some basic dimension text
        canvas.setFont("Helvetica", 8)
        
        y_pos = self.margin * mm + 10 * mm
        for key, value in parameters.items():
            if isinstance(value, (int, float)) and key not in ['template']:
                canvas.drawString(self.margin * mm, y_pos, f"{key}: {value}")
                y_pos += 10 * mm
                
    def _get_current_date(self) -> str:
        """Get current date as string"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d")
