"""
DXF Writer Module
Generates DXF files from parametric geometry
"""

import os
from typing import Dict, List, Tuple, Any
from .geometry import ParametricLayout


class DXFWriter:
    """Class for writing DXF files from parametric geometry"""
    
    def __init__(self):
        self.layout_engine = ParametricLayout()
        
    def write(self, parameters: Dict, output_path: str):
        """Write DXF file based on parameters"""
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Get template name from parameters
        template_name = parameters.get('template', 'straight')
        
        # Generate geometry
        geometry = self.layout_engine.generate_geometry(template_name, parameters)
        
        # Write DXF file
        with open(output_path, 'w') as f:
            self._write_dxf_header(f)
            self._write_dxf_entities(f, geometry)
            self._write_dxf_footer(f)
            
    def _write_dxf_header(self, file):
        """Write DXF file header"""
        file.write("0\n")
        file.write("SECTION\n")
        file.write("2\n")
        file.write("HEADER\n")
        file.write("9\n")
        file.write("$ACADVER\n")
        file.write("1\n")
        file.write("AC1015\n")
        file.write("9\n")
        file.write("$HANDSEED\n")
        file.write("5\n")
        file.write("FFFF\n")
        file.write("0\n")
        file.write("ENDSEC\n")
        
        # Tables section
        file.write("0\n")
        file.write("SECTION\n")
        file.write("2\n")
        file.write("TABLES\n")
        
        # Layer table
        file.write("0\n")
        file.write("TABLE\n")
        file.write("2\n")
        file.write("LAYER\n")
        file.write("5\n")
        file.write("2\n")
        file.write("100\n")
        file.write("AcDbSymbolTable\n")
        file.write("70\n")
        file.write("2\n")
        
        # Default layer
        file.write("0\n")
        file.write("LAYER\n")
        file.write("5\n")
        file.write("10\n")
        file.write("100\n")
        file.write("AcDbSymbolTableRecord\n")
        file.write("100\n")
        file.write("AcDbLayerTableRecord\n")
        file.write("2\n")
        file.write("0\n")
        file.write("70\n")
        file.write("0\n")
        file.write("6\n")
        file.write("CONTINUOUS\n")
        
        # Stringer layer
        file.write("0\n")
        file.write("LAYER\n")
        file.write("5\n")
        file.write("11\n")
        file.write("100\n")
        file.write("AcDbSymbolTableRecord\n")
        file.write("100\n")
        file.write("AcDbLayerTableRecord\n")
        file.write("2\n")
        file.write("STRINGERS\n")
        file.write("70\n")
        file.write("0\n")
        file.write("6\n")
        file.write("CONTINUOUS\n")
        file.write("62\n")
        file.write("1\n")  # Red color
        
        file.write("0\n")
        file.write("ENDTAB\n")
        file.write("0\n")
        file.write("ENDSEC\n")
        
        # Entities section header
        file.write("0\n")
        file.write("SECTION\n")
        file.write("2\n")
        file.write("ENTITIES\n")
        
    def _write_dxf_entities(self, file, geometry: Dict):
        """Write DXF entities from geometry"""
        for element_name, element in geometry.items():
            if element_name.startswith('stringer'):
                layer = "STRINGERS"
            else:
                layer = "0"
                
            self._write_geometry_element(file, element, layer)
            
    def _write_geometry_element(self, file, element: Dict, layer: str = "0"):
        """Write a single geometry element to DXF"""
        element_type = element.get('type', 'rectangle')
        
        if element_type == 'rectangle':
            self._write_rectangle(file, element, layer)
        elif element_type == 'l_shape':
            self._write_l_shape(file, element, layer)
        elif element_type == 'u_shape':
            self._write_u_shape(file, element, layer)
        elif element_type == 'array':
            for sub_element in element.get('elements', []):
                self._write_geometry_element(file, sub_element, layer)
                
    def _write_rectangle(self, file, element: Dict, layer: str):
        """Write a rectangle as a polyline"""
        vertices = element.get('vertices', [])
        if not vertices:
            return
            
        # Write as LWPOLYLINE
        file.write("0\n")
        file.write("LWPOLYLINE\n")
        file.write("5\n")
        file.write("100\n")  # Handle (simplified)
        file.write("100\n")
        file.write("AcDbEntity\n")
        file.write("8\n")
        file.write(f"{layer}\n")
        file.write("100\n")
        file.write("AcDbPolyline\n")
        file.write("90\n")
        file.write(f"{len(vertices)}\n")
        file.write("70\n")
        file.write("1\n")  # Closed polyline
        
        # Write vertices
        for x, y in vertices:
            file.write("10\n")
            file.write(f"{x:.3f}\n")
            file.write("20\n")
            file.write(f"{y:.3f}\n")
            
    def _write_l_shape(self, file, element: Dict, layer: str):
        """Write an L-shape as a polyline"""
        vertices = element.get('vertices', [])
        if not vertices:
            return
            
        # Write as LWPOLYLINE
        file.write("0\n")
        file.write("LWPOLYLINE\n")
        file.write("5\n")
        file.write("101\n")  # Handle (simplified)
        file.write("100\n")
        file.write("AcDbEntity\n")
        file.write("8\n")
        file.write(f"{layer}\n")
        file.write("100\n")
        file.write("AcDbPolyline\n")
        file.write("90\n")
        file.write(f"{len(vertices)}\n")
        file.write("70\n")
        file.write("1\n")  # Closed polyline
        
        # Write vertices
        for x, y in vertices:
            file.write("10\n")
            file.write(f"{x:.3f}\n")
            file.write("20\n")
            file.write(f"{y:.3f}\n")
            
    def _write_u_shape(self, file, element: Dict, layer: str):
        """Write a U-shape as polylines (outer and inner)"""
        vertices = element.get('vertices', {})
        
        # Write outer boundary
        outer_vertices = vertices.get('outer', [])
        if outer_vertices:
            file.write("0\n")
            file.write("LWPOLYLINE\n")
            file.write("5\n")
            file.write("102\n")  # Handle (simplified)
            file.write("100\n")
            file.write("AcDbEntity\n")
            file.write("8\n")
            file.write(f"{layer}\n")
            file.write("100\n")
            file.write("AcDbPolyline\n")
            file.write("90\n")
            file.write(f"{len(outer_vertices)}\n")
            file.write("70\n")
            file.write("1\n")  # Closed polyline
            
            for x, y in outer_vertices:
                file.write("10\n")
                file.write(f"{x:.3f}\n")
                file.write("20\n")
                file.write(f"{y:.3f}\n")
                
        # Write inner boundary (cutout)
        inner_vertices = vertices.get('inner', [])
        if inner_vertices:
            file.write("0\n")
            file.write("LWPOLYLINE\n")
            file.write("5\n")
            file.write("103\n")  # Handle (simplified)
            file.write("100\n")
            file.write("AcDbEntity\n")
            file.write("8\n")
            file.write(f"{layer}\n")
            file.write("100\n")
            file.write("AcDbPolyline\n")
            file.write("90\n")
            file.write(f"{len(inner_vertices)}\n")
            file.write("70\n")
            file.write("1\n")  # Closed polyline
            
            for x, y in inner_vertices:
                file.write("10\n")
                file.write(f"{x:.3f}\n")
                file.write("20\n")
                file.write(f"{y:.3f}\n")
                
    def _write_dxf_footer(self, file):
        """Write DXF file footer"""
        file.write("0\n")
        file.write("ENDSEC\n")
        file.write("0\n")
        file.write("EOF\n")
        
    def add_text_annotation(self, file, text: str, x: float, y: float, height: float = 10.0, layer: str = "0"):
        """Add text annotation to DXF"""
        file.write("0\n")
        file.write("TEXT\n")
        file.write("5\n")
        file.write("200\n")  # Handle (simplified)
        file.write("100\n")
        file.write("AcDbEntity\n")
        file.write("8\n")
        file.write(f"{layer}\n")
        file.write("100\n")
        file.write("AcDbText\n")
        file.write("10\n")
        file.write(f"{x:.3f}\n")
        file.write("20\n")
        file.write(f"{y:.3f}\n")
        file.write("30\n")
        file.write("0.0\n")
        file.write("40\n")
        file.write(f"{height:.3f}\n")
        file.write("1\n")
        file.write(f"{text}\n")
        
    def add_dimension(self, file, start_point: Tuple[float, float], end_point: Tuple[float, float], 
                     dimension_line_point: Tuple[float, float], text: str = "", layer: str = "0"):
        """Add dimension to DXF (simplified)"""
        # For now, just add lines and text
        # In a full implementation, you would use proper DXF dimension entities
        
        # Dimension line
        file.write("0\n")
        file.write("LINE\n")
        file.write("5\n")
        file.write("300\n")  # Handle (simplified)
        file.write("100\n")
        file.write("AcDbEntity\n")
        file.write("8\n")
        file.write(f"{layer}\n")
        file.write("100\n")
        file.write("AcDbLine\n")
        file.write("10\n")
        file.write(f"{start_point[0]:.3f}\n")
        file.write("20\n")
        file.write(f"{start_point[1]:.3f}\n")
        file.write("30\n")
        file.write("0.0\n")
        file.write("11\n")
        file.write(f"{end_point[0]:.3f}\n")
        file.write("21\n")
        file.write(f"{end_point[1]:.3f}\n")
        file.write("31\n")
        file.write("0.0\n")
        
        # Dimension text
        if text:
            mid_x = (start_point[0] + end_point[0]) / 2
            mid_y = (start_point[1] + end_point[1]) / 2
            self.add_text_annotation(file, text, mid_x, mid_y, 5.0, layer)
