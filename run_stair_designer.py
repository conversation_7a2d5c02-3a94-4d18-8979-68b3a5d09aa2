#!/usr/bin/env python3
"""
Launcher script for Stair Designer Application
This script launches the main application from the stair-designer directory
"""

import os
import sys
import subprocess

def main():
    """Launch the stair designer application"""
    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Path to the stair-designer directory
    stair_designer_dir = os.path.join(script_dir, 'stair-designer')
    
    # Path to the main.py file
    main_py_path = os.path.join(stair_designer_dir, 'main.py')
    
    # Check if the stair-designer directory exists
    if not os.path.exists(stair_designer_dir):
        print("Error: stair-designer directory not found!")
        print(f"Expected location: {stair_designer_dir}")
        return 1
        
    # Check if main.py exists
    if not os.path.exists(main_py_path):
        print("Error: main.py not found in stair-designer directory!")
        print(f"Expected location: {main_py_path}")
        return 1
        
    print("Starting Stair Designer Application...")
    print(f"Working directory: {stair_designer_dir}")
    
    try:
        # Change to the stair-designer directory and run main.py
        os.chdir(stair_designer_dir)
        
        # Run the application
        result = subprocess.run([sys.executable, 'main.py'], 
                              cwd=stair_designer_dir)
        
        return result.returncode
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        return 0
    except Exception as e:
        print(f"Error launching application: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
