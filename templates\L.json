{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "L-shaped layout template for corner applications", "type": "l_shape", "parameters": {"length_1": {"type": "float", "default": 1000.0, "min": 100.0, "max": 10000.0, "unit": "mm", "description": "Length of first leg"}, "length_2": {"type": "float", "default": 800.0, "min": 100.0, "max": 10000.0, "unit": "mm", "description": "Length of second leg"}, "width": {"type": "float", "default": 500.0, "min": 50.0, "max": 5000.0, "unit": "mm", "description": "Overall width of both legs"}, "thickness": {"type": "float", "default": 18.0, "min": 1.0, "max": 100.0, "unit": "mm", "description": "Material thickness"}, "corner_radius": {"type": "float", "default": 50.0, "min": 0.0, "max": 200.0, "unit": "mm", "description": "Radius of the inner corner"}, "stringer_spacing": {"type": "float", "default": 400.0, "min": 200.0, "max": 800.0, "unit": "mm", "description": "Spacing between stringers"}, "stringer_width": {"type": "float", "default": 50.0, "min": 20.0, "max": 200.0, "unit": "mm", "description": "Width of each stringer"}, "edge_margin": {"type": "float", "default": 25.0, "min": 10.0, "max": 100.0, "unit": "mm", "description": "Margin from edges"}}, "geometry": {"main_panel": {"type": "l_shape", "leg1": {"width": "{{width}}", "height": "{{length_1}}"}, "leg2": {"width": "{{length_2}}", "height": "{{width}}"}, "corner_radius": "{{corner_radius}}"}, "stringers_leg1": {"type": "array", "pattern": "linear", "count": "{{(width - 2 * edge_margin) / stringer_spacing}}", "element": {"type": "rectangle", "width": "{{stringer_width}}", "height": "{{length_1 - edge_margin - corner_radius}}", "position": {"x": "{{edge_margin + index * stringer_spacing}}", "y": "{{edge_margin}}"}}}, "stringers_leg2": {"type": "array", "pattern": "linear", "count": "{{(length_2 - corner_radius - 2 * edge_margin) / stringer_spacing}}", "element": {"type": "rectangle", "width": "{{stringer_width}}", "height": "{{width - edge_margin - corner_radius}}", "position": {"x": "{{width + edge_margin + index * stringer_spacing}}", "y": "{{length_1 - width + edge_margin}}"}, "rotation": 90}}}, "cut_list": [{"name": "Main L-Panel", "material": "Plywood", "thickness": "{{thickness}}", "description": "L-shaped main panel", "area": "{{length_1 * width + length_2 * width - width * width + corner_radius * corner_radius * (1 - 3.14159/4)}}", "quantity": 1}, {"name": "Stringer - Leg 1", "material": "Lumber", "thickness": "{{thickness}}", "width": "{{stringer_width}}", "length": "{{length_1 - edge_margin - corner_radius}}", "quantity": "{{(width - 2 * edge_margin) / stringer_spacing}}"}, {"name": "Stringer - Leg 2", "material": "Lumber", "thickness": "{{thickness}}", "width": "{{stringer_width}}", "length": "{{width - edge_margin - corner_radius}}", "quantity": "{{(length_2 - corner_radius - 2 * edge_margin) / stringer_spacing}}"}]}