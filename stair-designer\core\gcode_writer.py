"""
G-Code Writer Module
Generates G-Code files for RichAuto CNC controller for stair stringer cutting
"""

import os
import math
from typing import Dict, List, Tuple, Any


class GCodeWriter:
    """Class for writing G-Code files for RichAuto CNC controller"""

    def __init__(self):
        # RichAuto-specific settings for 1/2" compression bit
        self.feed_rate = 150  # inches/min (slower for wood)
        self.spindle_speed = 18000  # RPM
        self.plunge_rate = 50  # inches/min
        self.safe_height = 0.5  # inches
        self.cut_depth = -1.6  # inches (through 1.5" material)
        self.tool_diameter = 0.5  # inches (1/2" compression bit)
        self.stepdown = 0.25  # inches per pass
        
    def write_stair_gcode(self, stair_results: Dict, output_path: str):
        """Write G-Code file for stair stringer cutting"""
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Write G-Code file
        with open(output_path, 'w') as f:
            self._write_gcode_header(f, stair_results)
            self._write_stair_cutting_program(f, stair_results)
            self._write_gcode_footer(f)
            
    def _write_gcode_header(self, file, stair_results: Dict):
        """Write G-Code file header for RichAuto controller"""
        parameters = stair_results.get('parameters', {})
        job_name = parameters.get('job_name', 'Stair_Job')

        file.write(f"; RichAuto G-Code for Stair Stringer: {job_name}\n")
        file.write(f"; Generated by Stair Manufacturing Desktop App\n")
        file.write(";\n")
        file.write(f"; Tool: 1/2\" Compression Bit\n")
        file.write(f"; Material: {stair_results.get('material', 'LSL')} - 1.5\" thick\n")
        file.write(f"; Cut depth: {self.cut_depth}\" (through cut)\n")
        file.write(f"; Feed rate: {self.feed_rate} IPM\n")
        file.write(f"; Spindle speed: {self.spindle_speed} RPM\n")
        file.write(";\n")

        # RichAuto initialization
        file.write("G20 ; Set units to inches\n")
        file.write("G90 ; Absolute positioning\n")
        file.write("G17 ; XY plane selection\n")
        file.write("G94 ; Feed rate per minute\n")
        file.write("\n")

        # Machine setup
        file.write("; Machine setup\n")
        file.write("G28 ; Home all axes\n")
        file.write(f"M3 S{self.spindle_speed} ; Start spindle clockwise\n")
        file.write("G4 P3 ; Wait 3 seconds for spindle to reach speed\n")
        file.write(f"G0 Z{self.safe_height} ; Move to safe height\n")
        file.write("\n")

    def _write_stair_cutting_program(self, file, stair_results: Dict):
        """Write the main stair cutting program"""
        stringer_geom = stair_results.get('stringer_geometry', {})
        rise_run = stair_results.get('rise_run', {})

        if not stringer_geom or not rise_run:
            file.write("; Error: No valid stair geometry\n")
            return

        file.write("; Begin stair stringer cutting\n")
        file.write(f"; Steps: {rise_run.get('num_treads', 0)}\n")
        file.write(f"; Rise: {rise_run.get('riser_height', 0):.3f}\"\n")
        file.write(f"; Run: {rise_run.get('tread_depth', 0):.3f}\"\n")
        file.write("\n")

        # Get cut points
        cut_points = stringer_geom.get('cut_points', [])

        if not cut_points:
            file.write("; No cut points defined\n")
            return

        # Group cuts by step for efficient cutting
        steps = {}
        for cut in cut_points:
            step_num = cut.get('step_number', 1)
            if step_num not in steps:
                steps[step_num] = []
            steps[step_num].append(cut)

        # Cut each step
        for step_num in sorted(steps.keys()):
            file.write(f"; Cutting Step {step_num}\n")
            step_cuts = steps[step_num]

            # Find tread and riser cuts for this step
            tread_cut = None
            riser_cut = None

            for cut in step_cuts:
                if cut['type'] == 'tread':
                    tread_cut = cut
                elif cut['type'] == 'riser':
                    riser_cut = cut

            # Cut tread first (horizontal cut)
            if tread_cut:
                self._cut_line(file, tread_cut['start'], tread_cut['end'], f"Tread {step_num}")

            # Cut riser (vertical cut)
            if riser_cut:
                self._cut_line(file, riser_cut['start'], riser_cut['end'], f"Riser {step_num}")

            file.write("\n")

        file.write("; Stringer cutting complete\n")

    def _cut_line(self, file, start_point: Tuple[float, float], end_point: Tuple[float, float], description: str):
        """Cut a line with multiple passes if needed"""
        file.write(f"; {description}\n")

        # Calculate number of passes needed
        total_depth = abs(self.cut_depth)
        num_passes = max(1, math.ceil(total_depth / self.stepdown))

        # Move to start position
        file.write(f"G0 X{start_point[0]:.4f} Y{start_point[1]:.4f} ; Move to start\n")

        # Make multiple passes
        for pass_num in range(num_passes):
            current_depth = -min((pass_num + 1) * self.stepdown, total_depth)

            file.write(f"; Pass {pass_num + 1} of {num_passes} - Depth: {current_depth:.3f}\"\n")

            # Plunge to depth
            file.write(f"G1 Z{current_depth:.4f} F{self.plunge_rate} ; Plunge\n")

            # Cut to end point
            file.write(f"G1 X{end_point[0]:.4f} Y{end_point[1]:.4f} F{self.feed_rate} ; Cut\n")

            # Retract slightly for next pass (except last pass)
            if pass_num < num_passes - 1:
                file.write(f"G0 Z{current_depth + 0.1:.4f} ; Retract slightly\n")
                # Move back to start for next pass
                file.write(f"G0 X{start_point[0]:.4f} Y{start_point[1]:.4f} ; Return to start\n")

        # Final retract to safe height
        file.write(f"G0 Z{self.safe_height} ; Retract to safe height\n")
        
    def _write_gcode_program(self, file, geometry: Dict, parameters: Dict):
        """Write the main G-Code program"""
        # Process main panel first
        for element_name, element in geometry.items():
            if not element_name.startswith('stringer'):
                file.write(f"; Cutting {element_name}\n")
                self._cut_geometry_element(file, element)
                file.write("\n")
                
        # Process stringers
        for element_name, element in geometry.items():
            if element_name.startswith('stringer'):
                file.write(f"; Cutting {element_name}\n")
                self._cut_geometry_element(file, element)
                file.write("\n")
                
    def _cut_geometry_element(self, file, element: Dict):
        """Cut a single geometry element"""
        element_type = element.get('type', 'rectangle')
        
        if element_type == 'rectangle':
            self._cut_rectangle(file, element)
        elif element_type == 'l_shape':
            self._cut_l_shape(file, element)
        elif element_type == 'u_shape':
            self._cut_u_shape(file, element)
        elif element_type == 'array':
            for i, sub_element in enumerate(element.get('elements', [])):
                file.write(f"; Array element {i+1}\n")
                self._cut_geometry_element(file, sub_element)
                
    def _cut_rectangle(self, file, element: Dict):
        """Cut a rectangle"""
        vertices = element.get('vertices', [])
        if not vertices:
            return
            
        # Apply tool compensation (offset inward by tool radius)
        compensated_vertices = self._apply_tool_compensation(vertices, -self.tool_diameter/2)
        
        if not compensated_vertices:
            return
            
        # Move to start position
        start_x, start_y = compensated_vertices[0]
        file.write(f"G0 X{start_x:.3f} Y{start_y:.3f} ; Move to start position\n")
        file.write(f"G1 Z{self.cut_depth} F{self.plunge_rate} ; Plunge to cut depth\n")
        
        # Cut the perimeter
        file.write(f"F{self.feed_rate} ; Set cutting feed rate\n")
        for x, y in compensated_vertices[1:]:
            file.write(f"G1 X{x:.3f} Y{y:.3f}\n")
            
        # Close the loop
        file.write(f"G1 X{start_x:.3f} Y{start_y:.3f} ; Close the loop\n")
        
        # Retract
        file.write(f"G0 Z{self.safe_height} ; Retract to safe height\n")
        
    def _cut_l_shape(self, file, element: Dict):
        """Cut an L-shape"""
        vertices = element.get('vertices', [])
        if not vertices:
            return
            
        # Apply tool compensation
        compensated_vertices = self._apply_tool_compensation(vertices, -self.tool_diameter/2)
        
        if not compensated_vertices:
            return
            
        # Move to start position
        start_x, start_y = compensated_vertices[0]
        file.write(f"G0 X{start_x:.3f} Y{start_y:.3f} ; Move to start position\n")
        file.write(f"G1 Z{self.cut_depth} F{self.plunge_rate} ; Plunge to cut depth\n")
        
        # Cut the perimeter
        file.write(f"F{self.feed_rate} ; Set cutting feed rate\n")
        for x, y in compensated_vertices[1:]:
            file.write(f"G1 X{x:.3f} Y{y:.3f}\n")
            
        # Close the loop
        file.write(f"G1 X{start_x:.3f} Y{start_y:.3f} ; Close the loop\n")
        
        # Retract
        file.write(f"G0 Z{self.safe_height} ; Retract to safe height\n")
        
    def _cut_u_shape(self, file, element: Dict):
        """Cut a U-shape (outer boundary and inner cutout)"""
        vertices = element.get('vertices', {})
        
        # Cut outer boundary
        outer_vertices = vertices.get('outer', [])
        if outer_vertices:
            file.write("; Cutting outer boundary\n")
            compensated_outer = self._apply_tool_compensation(outer_vertices, -self.tool_diameter/2)
            
            if compensated_outer:
                start_x, start_y = compensated_outer[0]
                file.write(f"G0 X{start_x:.3f} Y{start_y:.3f}\n")
                file.write(f"G1 Z{self.cut_depth} F{self.plunge_rate}\n")
                file.write(f"F{self.feed_rate}\n")
                
                for x, y in compensated_outer[1:]:
                    file.write(f"G1 X{x:.3f} Y{y:.3f}\n")
                    
                file.write(f"G1 X{start_x:.3f} Y{start_y:.3f}\n")
                file.write(f"G0 Z{self.safe_height}\n")
                
        # Cut inner boundary (cutout)
        inner_vertices = vertices.get('inner', [])
        if inner_vertices:
            file.write("; Cutting inner cutout\n")
            compensated_inner = self._apply_tool_compensation(inner_vertices, self.tool_diameter/2)
            
            if compensated_inner:
                start_x, start_y = compensated_inner[0]
                file.write(f"G0 X{start_x:.3f} Y{start_y:.3f}\n")
                file.write(f"G1 Z{self.cut_depth} F{self.plunge_rate}\n")
                file.write(f"F{self.feed_rate}\n")
                
                for x, y in compensated_inner[1:]:
                    file.write(f"G1 X{x:.3f} Y{y:.3f}\n")
                    
                file.write(f"G1 X{start_x:.3f} Y{start_y:.3f}\n")
                file.write(f"G0 Z{self.safe_height}\n")
                
    def _apply_tool_compensation(self, vertices: List[Tuple[float, float]], offset: float) -> List[Tuple[float, float]]:
        """Apply tool compensation to a polygon"""
        if len(vertices) < 3:
            return vertices
            
        # Simple offset algorithm (for production use, consider a more robust library)
        compensated = []
        n = len(vertices)
        
        for i in range(n):
            # Get three consecutive points
            p1 = vertices[(i - 1) % n]
            p2 = vertices[i]
            p3 = vertices[(i + 1) % n]
            
            # Calculate edge vectors
            v1 = (p2[0] - p1[0], p2[1] - p1[1])
            v2 = (p3[0] - p2[0], p3[1] - p2[1])
            
            # Calculate edge normals (perpendicular vectors)
            n1 = self._normalize((-v1[1], v1[0]))
            n2 = self._normalize((-v2[1], v2[0]))
            
            # Calculate average normal
            avg_normal = ((n1[0] + n2[0]) / 2, (n1[1] + n2[1]) / 2)
            avg_normal = self._normalize(avg_normal)
            
            # Apply offset
            new_point = (p2[0] + avg_normal[0] * offset, p2[1] + avg_normal[1] * offset)
            compensated.append(new_point)
            
        return compensated
        
    def _normalize(self, vector: Tuple[float, float]) -> Tuple[float, float]:
        """Normalize a 2D vector"""
        length = math.sqrt(vector[0]**2 + vector[1]**2)
        if length == 0:
            return (0, 0)
        return (vector[0] / length, vector[1] / length)
        
    def _write_gcode_footer(self, file):
        """Write G-Code file footer"""
        file.write("; Program end\n")
        file.write("M5 ; Stop spindle\n")
        file.write("G0 Z50 ; Move to safe height\n")
        file.write("G0 X0 Y0 ; Return to origin\n")
        file.write("M30 ; Program end and rewind\n")
        
    def set_cutting_parameters(self, feed_rate: float = None, spindle_speed: float = None, 
                              plunge_rate: float = None, cut_depth: float = None, 
                              tool_diameter: float = None):
        """Set cutting parameters"""
        if feed_rate is not None:
            self.feed_rate = feed_rate
        if spindle_speed is not None:
            self.spindle_speed = spindle_speed
        if plunge_rate is not None:
            self.plunge_rate = plunge_rate
        if cut_depth is not None:
            self.cut_depth = cut_depth
        if tool_diameter is not None:
            self.tool_diameter = tool_diameter
