{"name": "L-Shaped Stair Configuration", "description": "L-shaped stair with landing and two flight segments", "type": "l_shaped_stair", "category": "stairs", "parameters": {"total_rise": {"type": "float", "default": 108.0, "min": 60.0, "max": 144.0, "unit": "inches", "description": "Total vertical rise of the stair", "step": 0.125}, "stair_width": {"type": "float", "default": 36.0, "min": 30.0, "max": 48.0, "unit": "inches", "description": "Width of both stair flights", "step": 1.0}, "landing_size": {"type": "float", "default": 36.0, "min": 30.0, "max": 60.0, "unit": "inches", "description": "Size of square landing (width x depth)", "step": 1.0}, "first_flight_steps": {"type": "integer", "default": 7, "min": 3, "max": 12, "description": "Number of steps in first flight"}, "tread_thickness": {"type": "string", "default": "1\"", "options": ["3/4\"", "1\"", "1-1/4\"", "1-1/2\"", "2\""], "description": "Thickness of tread material"}, "material": {"type": "string", "default": "LSL", "options": ["LSL", "LVL", "Solid Wood", "Plywood"], "description": "Stringer material type"}, "has_nosing": {"type": "boolean", "default": true, "description": "Include nosing on treads"}, "nosing_depth": {"type": "float", "default": 1.0, "min": 0.5, "max": 1.5, "unit": "inches", "description": "Depth of nosing overhang", "depends_on": "has_nosing"}, "riser_type": {"type": "string", "default": "Open", "options": ["Open", "Closed", "Mixed"], "description": "Type of riser configuration"}, "landing_height_ratio": {"type": "float", "default": 0.5, "min": 0.3, "max": 0.7, "step": 0.1, "description": "Ratio of total rise where landing occurs"}}, "calculations": {"landing_height": "total_rise * landing_height_ratio", "remaining_rise": "total_rise - landing_height", "first_flight_rise": "landing_height", "second_flight_rise": "remaining_rise", "optimal_riser_height": "total_rise / round(total_rise / 7.5)", "first_flight_riser_height": "first_flight_rise / first_flight_steps", "second_flight_steps": "round(second_flight_rise / optimal_riser_height)", "second_flight_riser_height": "second_flight_rise / second_flight_steps", "tread_depth": "25 - (2 * optimal_riser_height)", "first_flight_run": "tread_depth * (first_flight_steps - 1)", "second_flight_run": "tread_depth * (second_flight_steps - 1)", "first_stringer_length": "sqrt(first_flight_run^2 + first_flight_rise^2)", "second_stringer_length": "sqrt(second_flight_run^2 + second_flight_rise^2)", "stringer_count_per_flight": "stair_width <= 36 ? 3 : ceil(stair_width / 16)", "total_stringers": "stringer_count_per_flight * 2"}, "geometry": {"first_flight_stringers": {"type": "array", "count": "{{stringer_count_per_flight}}", "spacing": "{{stair_width / (stringer_count_per_flight - 1)}}", "element": {"type": "stringer_profile", "material": "{{material}}", "width": 11.25, "thickness": 1.5, "length": "{{first_stringer_length}}", "profile_type": "sawtooth", "flight": "first", "steps": "{{first_flight_steps}}", "riser_height": "{{first_flight_riser_height}}", "tread_depth": "{{tread_depth}}"}}, "second_flight_stringers": {"type": "array", "count": "{{stringer_count_per_flight}}", "spacing": "{{stair_width / (stringer_count_per_flight - 1)}}", "element": {"type": "stringer_profile", "material": "{{material}}", "width": 11.25, "thickness": 1.5, "length": "{{second_stringer_length}}", "profile_type": "sawtooth", "flight": "second", "steps": "{{second_flight_steps}}", "riser_height": "{{second_flight_riser_height}}", "tread_depth": "{{tread_depth}}", "orientation": "perpendicular"}}, "landing": {"type": "rectangle", "width": "{{landing_size}}", "depth": "{{landing_size}}", "thickness": "{{tread_thickness}}", "height": "{{landing_height}}", "framing": {"joist_spacing": 16, "joist_size": "2x10", "rim_board": "2x10"}}, "first_flight_treads": {"type": "array", "count": "{{first_flight_steps - 1}}", "element": {"type": "rectangle", "width": "{{stair_width}}", "depth": "{{tread_depth + (has_nosing ? nosing_depth : 0)}}", "thickness": "{{tread_thickness}}", "nosing": "{{has_nosing}}", "flight": "first"}}, "second_flight_treads": {"type": "array", "count": "{{second_flight_steps - 1}}", "element": {"type": "rectangle", "width": "{{stair_width}}", "depth": "{{tread_depth + (has_nosing ? nosing_depth : 0)}}", "thickness": "{{tread_thickness}}", "nosing": "{{has_nosing}}", "flight": "second"}}}, "cut_list": [{"name": "First Flight Stringers", "material": "{{material}}", "description": "2x12 stringer material for first flight", "thickness": "1-1/2\"", "width": "11-1/4\"", "length": "{{ceil(first_stringer_length / 12) * 12}}'", "quantity": "{{stringer_count_per_flight}}", "notes": "Cut to first flight profile", "board_feet": "{{(1.5 * 11.25 * ceil(first_stringer_length / 12) * 12 * stringer_count_per_flight) / 144}}"}, {"name": "Second Flight Stringers", "material": "{{material}}", "description": "2x12 stringer material for second flight", "thickness": "1-1/2\"", "width": "11-1/4\"", "length": "{{ceil(second_stringer_length / 12) * 12}}'", "quantity": "{{stringer_count_per_flight}}", "notes": "Cut to second flight profile", "board_feet": "{{(1.5 * 11.25 * ceil(second_stringer_length / 12) * 12 * stringer_count_per_flight) / 144}}"}, {"name": "Landing Joists", "material": "Lumber", "description": "2x10 landing frame joists", "thickness": "1-1/2\"", "width": "9-1/4\"", "length": "{{landing_size}}\"", "quantity": "{{ceil(landing_size / 16) + 1}}", "notes": "16\" on center spacing", "board_feet": "{{(1.5 * 9.25 * landing_size * (ceil(landing_size / 16) + 1)) / 144}}"}, {"name": "Landing Rim Board", "material": "Lumber", "description": "2x10 rim board for landing", "thickness": "1-1/2\"", "width": "9-1/4\"", "length": "Various", "quantity": 1, "notes": "Total length: {{landing_size * 4}}\" (cut to fit)", "board_feet": "{{(1.5 * 9.25 * landing_size * 4) / 144}}"}, {"name": "Landing Decking", "material": "Plywood", "description": "3/4\" plywood landing surface", "thickness": "3/4\"", "width": "{{landing_size}}\"", "depth": "{{landing_size}}\"", "quantity": 1, "notes": "Landing platform", "sheets_needed": "{{ceil((landing_size * landing_size) / (48 * 96))}}"}, {"name": "First Flight Treads", "material": "Hardwood", "description": "Tread boards for first flight", "thickness": "{{tread_thickness}}", "width": "{{stair_width}}\"", "length": "{{tread_depth + (has_nosing ? nosing_depth : 0)}}\"", "quantity": "{{first_flight_steps - 1}}", "notes": "{{has_nosing ? 'Include nosing profile' : 'Square edge'}}", "board_feet": "{{parse_thickness(tread_thickness) * stair_width * (tread_depth + (has_nosing ? nosing_depth : 0)) * (first_flight_steps - 1) / 144}}"}, {"name": "Second Flight Treads", "material": "Hardwood", "description": "Tread boards for second flight", "thickness": "{{tread_thickness}}", "width": "{{stair_width}}\"", "length": "{{tread_depth + (has_nosing ? nosing_depth : 0)}}\"", "quantity": "{{second_flight_steps - 1}}", "notes": "{{has_nosing ? 'Include nosing profile' : 'Square edge'}}", "board_feet": "{{parse_thickness(tread_thickness) * stair_width * (tread_depth + (has_nosing ? nosing_depth : 0)) * (second_flight_steps - 1) / 144}}"}], "building_codes": {"max_riser_height": 7.75, "min_riser_height": 7.0, "max_tread_depth": 11.0, "min_tread_depth": 10.0, "max_riser_variation": 0.375, "two_r_plus_t_min": 24.0, "two_r_plus_t_max": 25.0, "min_headroom": 80.0, "min_stair_width": 36.0, "min_landing_size": 30.0}, "validation_rules": [{"rule": "first_flight_riser_height >= building_codes.min_riser_height && first_flight_riser_height <= building_codes.max_riser_height", "message": "First flight riser height must be between 7\" and 7.75\""}, {"rule": "second_flight_riser_height >= building_codes.min_riser_height && second_flight_riser_height <= building_codes.max_riser_height", "message": "Second flight riser height must be between 7\" and 7.75\""}, {"rule": "abs(first_flight_riser_height - second_flight_riser_height) <= building_codes.max_riser_variation", "message": "Riser height variation between flights cannot exceed 3/8\""}, {"rule": "landing_size >= building_codes.min_landing_size", "message": "Landing size must be at least 30\""}, {"rule": "landing_size >= stair_width", "message": "Landing must be at least as wide as the stair"}], "output_settings": {"dxf": {"separate_flights": true, "include_landing_frame": true, "layers": {"first_flight": "FIRST_FLIGHT", "second_flight": "SECOND_FLIGHT", "landing": "LANDING", "dimensions": "DIMENSIONS"}}, "pdf": {"layout": "multi_view", "views": ["plan", "first_flight_elevation", "second_flight_elevation"], "include_landing_details": true}}}