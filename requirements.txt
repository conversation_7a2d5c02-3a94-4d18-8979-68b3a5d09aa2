# Stair Manufacturing Desktop Application Requirements

# Core GUI framework
PySide6>=6.4.0

# PDF generation (optional but recommended)
reportlab>=3.6.0

# Excel export support (optional)
openpyxl>=3.0.0

# DXF file generation
ezdxf>=1.0.0

# Additional math and geometry
numpy>=1.21.0

# JSON handling (built-in with Python)
# Math operations (built-in with Python)
# CSV handling (built-in with Python)
# OS operations (built-in with Python)
# Fractions handling (built-in with Python)

# Development and testing (optional)
pytest>=6.0.0
pytest-qt>=4.0.0

# Code formatting (optional)
black>=21.0.0
flake8>=3.9.0
