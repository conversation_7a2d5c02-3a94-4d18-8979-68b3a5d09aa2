#!/usr/bin/env python3
"""
Test script to verify the project structure and basic functionality
"""

import os
import sys
import json

def test_project_structure():
    """Test that all required directories and files exist"""
    print("Testing project structure...")

    required_files = [
        'main.py',
        'templates/straight.json',
        'templates/L.json',
        'templates/U.json',
        'core/geometry.py',
        'core/stair_geometry.py',
        'core/dxf_writer.py',
        'core/gcode_writer.py',
        'core/pdf_generator.py',
        'core/cutlist_generator.py',
        'core/cost_estimator.py',
        'assets/logo.png',
        'output/README.md',
        'config/pricing.json',
        'test_stair_app.py',
        'test_structure.py'
    ]

    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path}")

    # Store missing files for reporting
    test_project_structure.missing_files = missing_files

    if missing_files:
        print(f"\n❌ Missing files: {missing_files}")
        return False
    else:
        print("\n✅ All required files exist")
        return True

def test_template_files():
    """Test that template files are valid JSON"""
    print("\nTesting template files...")
    
    template_files = ['templates/straight.json', 'templates/L.json', 'templates/U.json']
    
    for template_file in template_files:
        try:
            with open(template_file, 'r') as f:
                template_data = json.load(f)
            
            # Check required fields
            required_fields = ['name', 'type', 'parameters', 'geometry', 'cut_list']
            for field in required_fields:
                if field not in template_data:
                    print(f"❌ {template_file}: Missing field '{field}'")
                    return False
            
            print(f"✓ {template_file}: Valid JSON with required fields")
            
        except json.JSONDecodeError as e:
            print(f"❌ {template_file}: Invalid JSON - {e}")
            return False
        except FileNotFoundError:
            print(f"❌ {template_file}: File not found")
            return False
    
    print("✅ All template files are valid")
    return True

def test_core_modules():
    """Test that core modules can be imported"""
    print("\nTesting core modules...")
    
    modules = [
        'core.geometry',
        'core.stair_geometry',
        'core.dxf_writer',
        'core.gcode_writer',
        'core.pdf_generator',
        'core.cutlist_generator',
        'core.cost_estimator'
    ]
    
    for module_name in modules:
        try:
            __import__(module_name)
            print(f"✓ {module_name}: Import successful")
        except ImportError as e:
            print(f"❌ {module_name}: Import failed - {e}")
            return False
    
    print("✅ All core modules can be imported")
    return True

def test_basic_functionality():
    """Test basic functionality of core modules"""
    print("\nTesting basic functionality...")
    
    try:
        from core.stair_geometry import StairGeometry

        # Test stair geometry calculation
        stair_geom = StairGeometry()

        # Test stair calculation
        test_params = {
            'job_name': 'test_job',
            'total_rise': 108.0,
            'stair_width': 36.0,
            'tread_thickness': '1',
            'riser_type': 'Open',
            'has_nosing': True,
            'material': 'LSL',
            'landing_height': 0,
            'landing_width': 36,
            'landing_depth': 36
        }

        results = stair_geom.calculate_stair(test_params)

        if not results.get('calculations_valid'):
            print("❌ Stair geometry calculation failed")
            return False

        print(f"✓ Stair geometry calculated successfully")
        print(f"  - Steps: {results['rise_run']['num_treads']}")
        print(f"  - Rise: {results['rise_run']['riser_height']:.3f}\"")

        # Test other modules
        from core.dxf_writer import DXFWriter
        from core.gcode_writer import GCodeWriter
        from core.pdf_generator import PDFGenerator
        from core.cutlist_generator import CutlistGenerator
        from core.cost_estimator import CostEstimator

        dxf_writer = DXFWriter()
        gcode_writer = GCodeWriter()
        pdf_generator = PDFGenerator()
        cutlist_generator = CutlistGenerator()
        cost_estimator = CostEstimator()

        print("✓ All core modules instantiated successfully")
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False
    
    print("✅ Basic functionality test passed")
    return True

def write_structure_results(results: dict):
    """Write structure test results to output folder"""
    os.makedirs("output", exist_ok=True)

    with open("output/structure_test_results.txt", "w") as f:
        f.write("STAIR DESIGNER - PROJECT STRUCTURE TEST RESULTS\n")
        f.write("=" * 60 + "\n\n")

        f.write(f"Test Date: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Tests: {results['total']}\n")
        f.write(f"Passed: {results['passed']}\n")
        f.write(f"Failed: {results['failed']}\n")
        f.write(f"Success Rate: {(results['passed']/results['total']*100):.1f}%\n\n")

        f.write("TEST DETAILS:\n")
        f.write("-" * 30 + "\n")
        for test_name, status in results['details'].items():
            status_symbol = "✓" if status else "❌"
            f.write(f"{status_symbol} {test_name}\n")

        if results['missing_files']:
            f.write(f"\nMISSING FILES:\n")
            f.write("-" * 30 + "\n")
            for file_path in results['missing_files']:
                f.write(f"❌ {file_path}\n")
        else:
            f.write(f"\n✓ All required files present\n")

def main():
    """Run all tests"""
    print("Stair Designer - Project Structure Test")
    print("=" * 50)
    
    tests = [
        ("Project Structure", test_project_structure),
        ("Template Files", test_template_files),
        ("Core Modules", test_core_modules),
        ("Basic Functionality", test_basic_functionality)
    ]

    passed = 0
    total = len(tests)
    test_details = {}

    for test_name, test_func in tests:
        test_result = test_func()
        test_details[test_name] = test_result
        if test_result:
            passed += 1

    # Collect results
    results_data = {
        'total': total,
        'passed': passed,
        'failed': total - passed,
        'details': test_details,
        'missing_files': getattr(test_project_structure, 'missing_files', [])
    }

    # Write results to output folder
    try:
        write_structure_results(results_data)
        print(f"\n📄 Structure test results written to: output/structure_test_results.txt")
    except Exception as e:
        print(f"\n⚠ Could not write test results: {e}")

    print("\n" + "=" * 50)
    print(f"STRUCTURE TEST RESULTS: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The project structure is ready.")
        print("\nTo run the application:")
        print("1. Install dependencies: pip install -r ../requirements.txt")
        print("2. Run the application: python main.py")
    else:
        print("❌ Some tests failed. Please check the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
