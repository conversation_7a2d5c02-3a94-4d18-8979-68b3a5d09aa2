{"name": "Straight Template", "description": "Simple straight layout template", "type": "straight", "parameters": {"length": {"type": "float", "default": 1000.0, "min": 100.0, "max": 10000.0, "unit": "mm", "description": "Overall length of the piece"}, "width": {"type": "float", "default": 500.0, "min": 50.0, "max": 5000.0, "unit": "mm", "description": "Overall width of the piece"}, "thickness": {"type": "float", "default": 18.0, "min": 1.0, "max": 100.0, "unit": "mm", "description": "Material thickness"}, "stringer_spacing": {"type": "float", "default": 400.0, "min": 200.0, "max": 800.0, "unit": "mm", "description": "Spacing between stringers"}, "stringer_width": {"type": "float", "default": 50.0, "min": 20.0, "max": 200.0, "unit": "mm", "description": "Width of each stringer"}, "edge_margin": {"type": "float", "default": 25.0, "min": 10.0, "max": 100.0, "unit": "mm", "description": "Margin from edges"}}, "geometry": {"main_panel": {"type": "rectangle", "width": "{{width}}", "height": "{{length}}"}, "stringers": {"type": "array", "pattern": "linear", "count": "{{(width - 2 * edge_margin) / stringer_spacing}}", "element": {"type": "rectangle", "width": "{{stringer_width}}", "height": "{{length - 2 * edge_margin}}", "position": {"x": "{{edge_margin + index * stringer_spacing}}", "y": "{{edge_margin}}"}}}}, "cut_list": [{"name": "Main Panel", "material": "Plywood", "thickness": "{{thickness}}", "width": "{{width}}", "length": "{{length}}", "quantity": 1}, {"name": "Stringer", "material": "Lumber", "thickness": "{{thickness}}", "width": "{{stringer_width}}", "length": "{{length - 2 * edge_margin}}", "quantity": "{{(width - 2 * edge_margin) / stringer_spacing}}"}]}