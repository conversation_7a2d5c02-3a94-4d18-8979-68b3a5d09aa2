"""
PDF Generator Module
Generates PDF layout drawings for stair manufacturing (8.5" x 11" format)
"""

import os
from typing import Dict, List, Tuple, Any


class PDFGenerator:
    """Class for generating PDF layout drawings for stair manufacturing"""

    def __init__(self):
        # US Letter size (8.5" x 11")
        self.page_width = 8.5  # inches
        self.page_height = 11.0  # inches
        self.margin = 0.5  # inches
        self.scale = 1.0

    def generate_stair_pdf(self, stair_results: Dict, output_path: str):
        """Generate PDF layout for stair project"""
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter
            from reportlab.lib.units import inch
            from reportlab.lib.colors import black, red, blue
        except ImportError:
            # Fallback to simple text-based PDF generation
            self._generate_simple_stair_pdf(stair_results, output_path)
            return

        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Create PDF
        c = canvas.Canvas(output_path, pagesize=letter)

        # Draw company header and title block
        self._draw_stair_title_block(c, stair_results)

        # Draw stair profile diagram
        self._draw_stair_profile(c, stair_results)

        # Draw cut list table
        self._draw_cut_list_table(c, stair_results)

        # Draw specifications and notes
        self._draw_specifications(c, stair_results)

        # Save PDF
        c.save()

    def _generate_simple_stair_pdf(self, stair_results: Dict, output_path: str):
        """Generate simple text-based PDF when reportlab not available"""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        parameters = stair_results.get('parameters', {})
        rise_run = stair_results.get('rise_run', {})

        with open(output_path, 'w') as f:
            f.write("STAIR MANUFACTURING LAYOUT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Job: {parameters.get('job_name', 'Untitled')}\n")
            f.write(f"Customer: {parameters.get('customer', '')}\n")
            f.write(f"Date: {parameters.get('date', '')}\n\n")

            f.write("STAIR SPECIFICATIONS:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Total Rise: {rise_run.get('total_rise', 0):.2f}\"\n")
            f.write(f"Total Run: {rise_run.get('total_run', 0):.2f}\"\n")
            f.write(f"Number of Steps: {rise_run.get('num_treads', 0)}\n")
            f.write(f"Riser Height: {rise_run.get('riser_height', 0):.3f}\"\n")
            f.write(f"Tread Depth: {rise_run.get('tread_depth', 0):.3f}\"\n")
            f.write(f"Stair Width: {parameters.get('stair_width', 36)}\"\n")
            f.write(f"Material: {stair_results.get('material', 'LSL')}\n\n")

            f.write("Note: Install 'reportlab' for full PDF with drawings.\n")
            f.write("pip install reportlab\n")

    def _draw_stair_title_block(self, canvas, stair_results: Dict):
        """Draw title block with company info and job details"""
        from reportlab.lib.units import inch

        parameters = stair_results.get('parameters', {})

        # Company header
        canvas.setFont("Helvetica-Bold", 16)
        canvas.drawString(0.5 * inch, 10.5 * inch, "Treads By Design")

        canvas.setFont("Helvetica", 10)
        canvas.drawString(0.5 * inch, 10.2 * inch, "Stair Manufacturing & Design")
        canvas.drawString(0.5 * inch, 10.0 * inch, "Phone: (*************")

        # Title block border
        canvas.rect(5.5 * inch, 9.5 * inch, 2.5 * inch, 1.5 * inch)

        # Job information
        canvas.setFont("Helvetica-Bold", 12)
        canvas.drawString(5.7 * inch, 10.7 * inch, "JOB INFORMATION")

        canvas.setFont("Helvetica", 10)
        canvas.drawString(5.7 * inch, 10.4 * inch, f"Job: {parameters.get('job_name', 'Untitled')}")
        canvas.drawString(5.7 * inch, 10.2 * inch, f"Customer: {parameters.get('customer', '')}")
        canvas.drawString(5.7 * inch, 10.0 * inch, f"Date: {parameters.get('date', '')}")
        canvas.drawString(5.7 * inch, 9.8 * inch, f"Material: {stair_results.get('material', 'LSL')}")

    def _draw_stair_profile(self, canvas, stair_results: Dict):
        """Draw stair profile diagram"""
        from reportlab.lib.units import inch

        rise_run = stair_results.get('rise_run', {})
        if not rise_run:
            return

        # Drawing area
        start_x = 0.5 * inch
        start_y = 6.0 * inch
        scale_factor = 0.02  # Scale for drawing (inches per inch)

        num_treads = rise_run.get('num_treads', 0)
        riser_height = rise_run.get('riser_height', 7.5)
        tread_depth = rise_run.get('tread_depth', 10)

        canvas.setFont("Helvetica-Bold", 12)
        canvas.drawString(start_x, start_y + 2 * inch, "STAIR PROFILE")

        # Draw stair steps
        canvas.setStrokeColor(black)
        canvas.setLineWidth(1)

        current_x = start_x
        current_y = start_y

        for step in range(num_treads + 1):  # Include top landing
            # Draw riser (vertical line)
            if step < num_treads:
                canvas.line(current_x, current_y,
                           current_x, current_y + riser_height * scale_factor)

            # Draw tread (horizontal line)
            if step < num_treads:
                canvas.line(current_x, current_y + riser_height * scale_factor,
                           current_x + tread_depth * scale_factor,
                           current_y + riser_height * scale_factor)

            current_x += tread_depth * scale_factor
            current_y += riser_height * scale_factor

        # Add dimensions
        canvas.setFont("Helvetica", 8)
        canvas.drawString(start_x, start_y - 0.3 * inch,
                         f"Rise: {riser_height:.3f}\"")
        canvas.drawString(start_x + 1 * inch, start_y - 0.3 * inch,
                         f"Run: {tread_depth:.3f}\"")
        canvas.drawString(start_x + 2 * inch, start_y - 0.3 * inch,
                         f"Steps: {num_treads}")

    def _draw_cut_list_table(self, canvas, stair_results: Dict):
        """Draw cut list table"""
        from reportlab.lib.units import inch

        # Import cut list from stair geometry
        from .stair_geometry import StairGeometry
        stair_geom = StairGeometry()
        stair_geom.results = stair_results
        cut_list = stair_geom.get_cut_list()

        # Table position
        table_x = 0.5 * inch
        table_y = 4.0 * inch
        row_height = 0.25 * inch
        col_widths = [1.5 * inch, 2.0 * inch, 1.0 * inch, 2.0 * inch, 0.5 * inch]

        canvas.setFont("Helvetica-Bold", 12)
        canvas.drawString(table_x, table_y + 0.5 * inch, "CUT LIST")

        # Table headers
        headers = ["Item", "Description", "Material", "Dimensions", "Qty"]
        canvas.setFont("Helvetica-Bold", 10)

        current_x = table_x
        for i, header in enumerate(headers):
            canvas.drawString(current_x, table_y, header)
            current_x += col_widths[i]

        # Table data
        canvas.setFont("Helvetica", 9)
        current_y = table_y - row_height

        for item in cut_list:
            current_x = table_x

            # Draw row data
            row_data = [
                item.get('item', ''),
                item.get('description', ''),
                item.get('material', ''),
                item.get('dimensions', ''),
                str(item.get('quantity', 1))
            ]

            for i, data in enumerate(row_data):
                canvas.drawString(current_x, current_y, str(data)[:20])  # Truncate if too long
                current_x += col_widths[i]

            current_y -= row_height

        # Draw table borders
        table_width = sum(col_widths)
        table_height = (len(cut_list) + 1) * row_height
        canvas.rect(table_x, current_y, table_width, table_height)

    def _draw_specifications(self, canvas, stair_results: Dict):
        """Draw specifications and building code compliance"""
        from reportlab.lib.units import inch

        rise_run = stair_results.get('rise_run', {})
        parameters = stair_results.get('parameters', {})

        spec_x = 0.5 * inch
        spec_y = 2.0 * inch

        canvas.setFont("Helvetica-Bold", 12)
        canvas.drawString(spec_x, spec_y + 0.5 * inch, "SPECIFICATIONS & CODE COMPLIANCE")

        canvas.setFont("Helvetica", 10)

        tread_thickness = parameters.get('tread_thickness', '1"')
        specs = [
            f"Total Rise: {rise_run.get('total_rise', 0):.2f}\"",
            f"Total Run: {rise_run.get('total_run', 0):.2f}\"",
            f"Riser Height: {rise_run.get('riser_height', 0):.3f}\"",
            f"Tread Depth: {rise_run.get('tread_depth', 0):.3f}\"",
            f"Stair Width: {parameters.get('stair_width', 36)}\"",
            f"Tread Thickness: {tread_thickness}",
            f"Nosing: {'Yes' if parameters.get('has_nosing') else 'No'}",
            ""
        ]

        # Code compliance check
        riser_height = rise_run.get('riser_height', 0)
        tread_depth = rise_run.get('tread_depth', 0)
        two_r_plus_t = 2 * riser_height + tread_depth

        compliance_status = "✓ Compliant" if 24 <= two_r_plus_t <= 25 else "⚠ Check compliance"
        riser_status = "✓" if 7 <= riser_height <= 7.75 else "⚠"
        tread_status = "✓" if 10 <= tread_depth <= 11 else "⚠"

        specs.extend([
            "CODE COMPLIANCE:",
            f"2R + T = {two_r_plus_t:.2f}\" (should be 24-25\")",
            compliance_status,
            f"Riser range: 7\"-7.75\" ({riser_status})",
            f"Tread range: 10\"-11\" ({tread_status})"
        ])

        current_y = spec_y
        for spec in specs:
            canvas.drawString(spec_x, current_y, spec)
            current_y -= 0.15 * inch
        
    def generate(self, parameters: Dict, output_path: str):
        """Generate PDF layout based on parameters"""
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import A4
            from reportlab.lib.units import mm
            from reportlab.lib.colors import black, red, blue
        except ImportError:
            # Fallback to simple text-based PDF generation
            self._generate_simple_pdf(parameters, output_path)
            return
            
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Get template name from parameters
        template_name = parameters.get('template', 'straight')
        
        # Generate geometry
        geometry = self.layout_engine.generate_geometry(template_name, parameters)
        
        # Calculate scale to fit on page
        self._calculate_scale(geometry)
        
        # Create PDF
        c = canvas.Canvas(output_path, pagesize=A4)
        
        # Draw title block
        self._draw_title_block(c, parameters)
        
        # Draw geometry
        self._draw_geometry(c, geometry)
        
        # Draw dimensions
        self._draw_dimensions(c, geometry, parameters)
        
        # Save PDF
        c.save()
        
    def _generate_simple_pdf(self, parameters: Dict, output_path: str):
        """Generate a simple text-based PDF when reportlab is not available"""
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Get template name from parameters
        template_name = parameters.get('template', 'straight')
        
        # Generate geometry
        geometry = self.layout_engine.generate_geometry(template_name, parameters)
        
        # Create a simple text file with .pdf extension
        with open(output_path, 'w') as f:
            f.write("AutoCAD Desktop - Layout Drawing\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Job Name: {parameters.get('job_name', 'myjob')}\n")
            f.write(f"Template: {template_name}\n")
            f.write(f"Date: {self._get_current_date()}\n\n")
            
            f.write("Parameters:\n")
            f.write("-" * 20 + "\n")
            for key, value in parameters.items():
                if key not in ['template', 'job_name']:
                    f.write(f"{key}: {value}\n")
            f.write("\n")
            
            f.write("Geometry Summary:\n")
            f.write("-" * 20 + "\n")
            for element_name, element in geometry.items():
                f.write(f"{element_name}: {element.get('type', 'unknown')}\n")
                if element.get('type') == 'rectangle':
                    f.write(f"  Size: {element.get('width', 0):.1f} x {element.get('height', 0):.1f} mm\n")
                elif element.get('type') == 'array':
                    f.write(f"  Count: {element.get('count', 0)} elements\n")
            f.write("\n")
            
            f.write("Note: Install 'reportlab' package for full PDF generation with drawings.\n")
            f.write("pip install reportlab\n")
            
    def _calculate_scale(self, geometry: Dict):
        """Calculate scale to fit geometry on page"""
        # Find overall bounding box
        min_x, min_y, max_x, max_y = float('inf'), float('inf'), float('-inf'), float('-inf')
        
        for element in geometry.values():
            bbox = self.layout_engine.get_bounding_box(element)
            min_x = min(min_x, bbox['min_x'])
            min_y = min(min_y, bbox['min_y'])
            max_x = max(max_x, bbox['max_x'])
            max_y = max(max_y, bbox['max_y'])
            
        if min_x == float('inf'):  # No geometry
            self.scale = 1.0
            return
            
        # Calculate required dimensions
        width = max_x - min_x
        height = max_y - min_y
        
        # Available space (accounting for margins and title block)
        available_width = self.page_width - 2 * self.margin
        available_height = self.page_height - 2 * self.margin - 50  # 50mm for title block
        
        # Calculate scale
        scale_x = available_width / width if width > 0 else 1.0
        scale_y = available_height / height if height > 0 else 1.0
        
        self.scale = min(scale_x, scale_y, 1.0)  # Don't scale up
        
    def _draw_title_block(self, canvas, parameters: Dict):
        """Draw title block on PDF"""
        from reportlab.lib.units import mm
        
        # Title block rectangle
        x = self.margin * mm
        y = (self.page_height - self.margin - 40) * mm
        width = (self.page_width - 2 * self.margin) * mm
        height = 35 * mm
        
        canvas.rect(x, y, width, height)
        
        # Title
        canvas.setFont("Helvetica-Bold", 16)
        canvas.drawString(x + 5*mm, y + height - 10*mm, "AutoCAD Desktop - Layout Drawing")
        
        # Job information
        canvas.setFont("Helvetica", 10)
        canvas.drawString(x + 5*mm, y + height - 20*mm, f"Job: {parameters.get('job_name', 'myjob')}")
        canvas.drawString(x + 5*mm, y + height - 28*mm, f"Template: {parameters.get('template', 'straight')}")
        canvas.drawString(x + width - 60*mm, y + height - 20*mm, f"Date: {self._get_current_date()}")
        canvas.drawString(x + width - 60*mm, y + height - 28*mm, f"Scale: 1:{1/self.scale:.0f}")
        
    def _draw_geometry(self, canvas, geometry: Dict):
        """Draw geometry on PDF"""
        from reportlab.lib.units import mm
        from reportlab.lib.colors import black, red
        
        # Drawing area offset
        offset_x = self.margin * mm
        offset_y = self.margin * mm
        
        for element_name, element in geometry.items():
            # Set color based on element type
            if element_name.startswith('stringer'):
                canvas.setStrokeColor(red)
            else:
                canvas.setStrokeColor(black)
                
            self._draw_geometry_element(canvas, element, offset_x, offset_y)
            
    def _draw_geometry_element(self, canvas, element: Dict, offset_x: float, offset_y: float):
        """Draw a single geometry element"""
        from reportlab.lib.units import mm
        
        element_type = element.get('type', 'rectangle')
        
        if element_type == 'rectangle':
            self._draw_rectangle_pdf(canvas, element, offset_x, offset_y)
        elif element_type == 'l_shape':
            self._draw_l_shape_pdf(canvas, element, offset_x, offset_y)
        elif element_type == 'u_shape':
            self._draw_u_shape_pdf(canvas, element, offset_x, offset_y)
        elif element_type == 'array':
            for sub_element in element.get('elements', []):
                self._draw_geometry_element(canvas, sub_element, offset_x, offset_y)
                
    def _draw_rectangle_pdf(self, canvas, element: Dict, offset_x: float, offset_y: float):
        """Draw a rectangle on PDF"""
        from reportlab.lib.units import mm
        
        vertices = element.get('vertices', [])
        if len(vertices) < 3:
            return
            
        # Convert to PDF coordinates
        path = canvas.beginPath()
        first_vertex = True
        
        for x, y in vertices:
            pdf_x = offset_x + x * self.scale * mm
            pdf_y = offset_y + y * self.scale * mm
            
            if first_vertex:
                path.moveTo(pdf_x, pdf_y)
                first_vertex = False
            else:
                path.lineTo(pdf_x, pdf_y)
                
        path.close()
        canvas.drawPath(path)
        
    def _draw_l_shape_pdf(self, canvas, element: Dict, offset_x: float, offset_y: float):
        """Draw an L-shape on PDF"""
        # Same as rectangle for now
        self._draw_rectangle_pdf(canvas, element, offset_x, offset_y)
        
    def _draw_u_shape_pdf(self, canvas, element: Dict, offset_x: float, offset_y: float):
        """Draw a U-shape on PDF"""
        from reportlab.lib.units import mm
        
        vertices = element.get('vertices', {})
        
        # Draw outer boundary
        outer_vertices = vertices.get('outer', [])
        if outer_vertices:
            path = canvas.beginPath()
            first_vertex = True
            
            for x, y in outer_vertices:
                pdf_x = offset_x + x * self.scale * mm
                pdf_y = offset_y + y * self.scale * mm
                
                if first_vertex:
                    path.moveTo(pdf_x, pdf_y)
                    first_vertex = False
                else:
                    path.lineTo(pdf_x, pdf_y)
                    
            path.close()
            canvas.drawPath(path)
            
        # Draw inner boundary
        inner_vertices = vertices.get('inner', [])
        if inner_vertices:
            path = canvas.beginPath()
            first_vertex = True
            
            for x, y in inner_vertices:
                pdf_x = offset_x + x * self.scale * mm
                pdf_y = offset_y + y * self.scale * mm
                
                if first_vertex:
                    path.moveTo(pdf_x, pdf_y)
                    first_vertex = False
                else:
                    path.lineTo(pdf_x, pdf_y)
                    
            path.close()
            canvas.drawPath(path)
            
    def _draw_dimensions(self, canvas, geometry: Dict, parameters: Dict):
        """Draw dimensions on PDF"""
        from reportlab.lib.units import mm
        
        # For now, just add some basic dimension text
        canvas.setFont("Helvetica", 8)
        
        y_pos = self.margin * mm + 10 * mm
        for key, value in parameters.items():
            if isinstance(value, (int, float)) and key not in ['template']:
                canvas.drawString(self.margin * mm, y_pos, f"{key}: {value}")
                y_pos += 10 * mm
                
    def _get_current_date(self) -> str:
        """Get current date as string"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d")
