#!/usr/bin/env python3
"""
Test script to verify the project structure and basic functionality
"""

import os
import sys
import json

def test_project_structure():
    """Test that all required directories and files exist"""
    print("Testing project structure...")
    
    required_files = [
        'main.py',
        'requirements.txt',
        'README.md',
        'templates/straight.json',
        'templates/L.json',
        'templates/U.json',
        'core/geometry.py',
        'core/dxf_writer.py',
        'core/gcode_writer.py',
        'core/pdf_generator.py',
        'core/cutlist_generator.py',
        'assets/logo.png',
        'output/README.md'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path}")
    
    if missing_files:
        print(f"\n❌ Missing files: {missing_files}")
        return False
    else:
        print("\n✅ All required files exist")
        return True

def test_template_files():
    """Test that template files are valid JSON"""
    print("\nTesting template files...")
    
    template_files = ['templates/straight.json', 'templates/L.json', 'templates/U.json']
    
    for template_file in template_files:
        try:
            with open(template_file, 'r') as f:
                template_data = json.load(f)
            
            # Check required fields
            required_fields = ['name', 'type', 'parameters', 'geometry', 'cut_list']
            for field in required_fields:
                if field not in template_data:
                    print(f"❌ {template_file}: Missing field '{field}'")
                    return False
            
            print(f"✓ {template_file}: Valid JSON with required fields")
            
        except json.JSONDecodeError as e:
            print(f"❌ {template_file}: Invalid JSON - {e}")
            return False
        except FileNotFoundError:
            print(f"❌ {template_file}: File not found")
            return False
    
    print("✅ All template files are valid")
    return True

def test_core_modules():
    """Test that core modules can be imported"""
    print("\nTesting core modules...")
    
    modules = [
        'core.geometry',
        'core.dxf_writer',
        'core.gcode_writer',
        'core.pdf_generator',
        'core.cutlist_generator'
    ]
    
    for module_name in modules:
        try:
            __import__(module_name)
            print(f"✓ {module_name}: Import successful")
        except ImportError as e:
            print(f"❌ {module_name}: Import failed - {e}")
            return False
    
    print("✅ All core modules can be imported")
    return True

def test_basic_functionality():
    """Test basic functionality of core modules"""
    print("\nTesting basic functionality...")
    
    try:
        from core.geometry import ParametricLayout
        
        # Test template loading
        layout = ParametricLayout()
        templates = layout.templates
        
        if not templates:
            print("❌ No templates loaded")
            return False
        
        print(f"✓ Loaded {len(templates)} templates: {list(templates.keys())}")
        
        # Test geometry generation
        test_params = {
            'template': 'straight',
            'length': 1000.0,
            'width': 500.0,
            'thickness': 18.0,
            'stringer_spacing': 400.0,
            'stringer_width': 50.0,
            'edge_margin': 25.0
        }
        
        geometry = layout.generate_geometry('straight', test_params)
        
        if not geometry:
            print("❌ No geometry generated")
            return False
        
        print(f"✓ Generated geometry with {len(geometry)} elements")
        
        # Test other modules
        from core.dxf_writer import DXFWriter
        from core.gcode_writer import GCodeWriter
        from core.pdf_generator import PDFGenerator
        from core.cutlist_generator import CutlistGenerator
        
        dxf_writer = DXFWriter()
        gcode_writer = GCodeWriter()
        pdf_generator = PDFGenerator()
        cutlist_generator = CutlistGenerator()
        
        print("✓ All core modules instantiated successfully")
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False
    
    print("✅ Basic functionality test passed")
    return True

def main():
    """Run all tests"""
    print("AutoCAD Desktop - Project Structure Test")
    print("=" * 50)
    
    tests = [
        test_project_structure,
        test_template_files,
        test_core_modules,
        test_basic_functionality
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! The project structure is ready.")
        print("\nTo run the application:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Run the application: python main.py")
    else:
        print("❌ Some tests failed. Please check the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
