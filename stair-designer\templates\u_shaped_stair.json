{"name": "U-Shaped Stair Configuration", "description": "U-shaped stair with two landings and three flight segments", "type": "u_shaped_stair", "category": "stairs", "parameters": {"total_rise": {"type": "float", "default": 108.0, "min": 84.0, "max": 144.0, "unit": "inches", "description": "Total vertical rise of the stair", "step": 0.125}, "stair_width": {"type": "float", "default": 36.0, "min": 30.0, "max": 48.0, "unit": "inches", "description": "Width of all stair flights", "step": 1.0}, "well_width": {"type": "float", "default": 48.0, "min": 36.0, "max": 72.0, "unit": "inches", "description": "Width of the stair well opening", "step": 1.0}, "well_length": {"type": "float", "default": 120.0, "min": 96.0, "max": 180.0, "unit": "inches", "description": "Length of the stair well opening", "step": 1.0}, "landing_size": {"type": "float", "default": 36.0, "min": 30.0, "max": 60.0, "unit": "inches", "description": "Size of intermediate and top landings", "step": 1.0}, "first_flight_steps": {"type": "integer", "default": 6, "min": 3, "max": 10, "description": "Number of steps in first flight"}, "second_flight_steps": {"type": "integer", "default": 6, "min": 3, "max": 10, "description": "Number of steps in second flight"}, "tread_thickness": {"type": "string", "default": "1\"", "options": ["3/4\"", "1\"", "1-1/4\"", "1-1/2\"", "2\""], "description": "Thickness of tread material"}, "material": {"type": "string", "default": "LSL", "options": ["LSL", "LVL", "Solid Wood", "Plywood"], "description": "Stringer material type"}, "has_nosing": {"type": "boolean", "default": true, "description": "Include nosing on treads"}, "nosing_depth": {"type": "float", "default": 1.0, "min": 0.5, "max": 1.5, "unit": "inches", "description": "Depth of nosing overhang", "depends_on": "has_nosing"}, "riser_type": {"type": "string", "default": "Open", "options": ["Open", "Closed", "Mixed"], "description": "Type of riser configuration"}}, "calculations": {"total_steps": "first_flight_steps + second_flight_steps", "third_flight_steps": "round((total_rise - (first_flight_steps + second_flight_steps) * (total_rise / (total_steps + 2))) / (total_rise / (total_steps + 2)))", "optimal_riser_height": "total_rise / (first_flight_steps + second_flight_steps + third_flight_steps)", "first_flight_rise": "first_flight_steps * optimal_riser_height", "second_flight_rise": "second_flight_steps * optimal_riser_height", "third_flight_rise": "third_flight_steps * optimal_riser_height", "tread_depth": "25 - (2 * optimal_riser_height)", "first_flight_run": "tread_depth * (first_flight_steps - 1)", "second_flight_run": "tread_depth * (second_flight_steps - 1)", "third_flight_run": "tread_depth * (third_flight_steps - 1)", "first_stringer_length": "sqrt(first_flight_run^2 + first_flight_rise^2)", "second_stringer_length": "sqrt(second_flight_run^2 + second_flight_rise^2)", "third_stringer_length": "sqrt(third_flight_run^2 + third_flight_rise^2)", "stringer_count_per_flight": "stair_width <= 36 ? 3 : ceil(stair_width / 16)", "total_stringers": "stringer_count_per_flight * 3", "intermediate_landing_height": "first_flight_rise", "top_landing_height": "first_flight_rise + second_flight_rise"}, "geometry": {"first_flight_stringers": {"type": "array", "count": "{{stringer_count_per_flight}}", "spacing": "{{stair_width / (stringer_count_per_flight - 1)}}", "element": {"type": "stringer_profile", "material": "{{material}}", "width": 11.25, "thickness": 1.5, "length": "{{first_stringer_length}}", "profile_type": "sawtooth", "flight": "first", "steps": "{{first_flight_steps}}", "riser_height": "{{optimal_riser_height}}", "tread_depth": "{{tread_depth}}", "orientation": "up"}}, "second_flight_stringers": {"type": "array", "count": "{{stringer_count_per_flight}}", "spacing": "{{stair_width / (stringer_count_per_flight - 1)}}", "element": {"type": "stringer_profile", "material": "{{material}}", "width": 11.25, "thickness": 1.5, "length": "{{second_stringer_length}}", "profile_type": "sawtooth", "flight": "second", "steps": "{{second_flight_steps}}", "riser_height": "{{optimal_riser_height}}", "tread_depth": "{{tread_depth}}", "orientation": "down"}}, "third_flight_stringers": {"type": "array", "count": "{{stringer_count_per_flight}}", "spacing": "{{stair_width / (stringer_count_per_flight - 1)}}", "element": {"type": "stringer_profile", "material": "{{material}}", "width": 11.25, "thickness": 1.5, "length": "{{third_stringer_length}}", "profile_type": "sawtooth", "flight": "third", "steps": "{{third_flight_steps}}", "riser_height": "{{optimal_riser_height}}", "tread_depth": "{{tread_depth}}", "orientation": "up"}}, "intermediate_landing": {"type": "rectangle", "width": "{{landing_size}}", "depth": "{{landing_size}}", "thickness": "{{tread_thickness}}", "height": "{{intermediate_landing_height}}", "position": "between_first_second", "framing": {"joist_spacing": 16, "joist_size": "2x10", "rim_board": "2x10"}}, "top_landing": {"type": "rectangle", "width": "{{landing_size}}", "depth": "{{landing_size}}", "thickness": "{{tread_thickness}}", "height": "{{top_landing_height}}", "position": "between_second_third", "framing": {"joist_spacing": 16, "joist_size": "2x10", "rim_board": "2x10"}}, "well_opening": {"type": "rectangle", "width": "{{well_width}}", "length": "{{well_length}}", "description": "Stair well opening in floor above"}}, "cut_list": [{"name": "First Flight Stringers", "material": "{{material}}", "description": "2x12 stringer material for first flight", "thickness": "1-1/2\"", "width": "11-1/4\"", "length": "{{ceil(first_stringer_length / 12) * 12}}'", "quantity": "{{stringer_count_per_flight}}", "notes": "Cut to first flight profile", "board_feet": "{{(1.5 * 11.25 * ceil(first_stringer_length / 12) * 12 * stringer_count_per_flight) / 144}}"}, {"name": "Second Flight Stringers", "material": "{{material}}", "description": "2x12 stringer material for second flight", "thickness": "1-1/2\"", "width": "11-1/4\"", "length": "{{ceil(second_stringer_length / 12) * 12}}'", "quantity": "{{stringer_count_per_flight}}", "notes": "Cut to second flight profile", "board_feet": "{{(1.5 * 11.25 * ceil(second_stringer_length / 12) * 12 * stringer_count_per_flight) / 144}}"}, {"name": "Third Flight Stringers", "material": "{{material}}", "description": "2x12 stringer material for third flight", "thickness": "1-1/2\"", "width": "11-1/4\"", "length": "{{ceil(third_stringer_length / 12) * 12}}'", "quantity": "{{stringer_count_per_flight}}", "notes": "Cut to third flight profile", "board_feet": "{{(1.5 * 11.25 * ceil(third_stringer_length / 12) * 12 * stringer_count_per_flight) / 144}}"}, {"name": "Intermediate Landing Joists", "material": "Lumber", "description": "2x10 intermediate landing frame joists", "thickness": "1-1/2\"", "width": "9-1/4\"", "length": "{{landing_size}}\"", "quantity": "{{ceil(landing_size / 16) + 1}}", "notes": "16\" on center spacing", "board_feet": "{{(1.5 * 9.25 * landing_size * (ceil(landing_size / 16) + 1)) / 144}}"}, {"name": "Top Landing Joists", "material": "Lumber", "description": "2x10 top landing frame joists", "thickness": "1-1/2\"", "width": "9-1/4\"", "length": "{{landing_size}}\"", "quantity": "{{ceil(landing_size / 16) + 1}}", "notes": "16\" on center spacing", "board_feet": "{{(1.5 * 9.25 * landing_size * (ceil(landing_size / 16) + 1)) / 144}}"}, {"name": "Landing Rim Boards", "material": "Lumber", "description": "2x10 rim boards for both landings", "thickness": "1-1/2\"", "width": "9-1/4\"", "length": "Various", "quantity": 1, "notes": "Total length: {{landing_size * 8}}\" (cut to fit both landings)", "board_feet": "{{(1.5 * 9.25 * landing_size * 8) / 144}}"}, {"name": "Landing Decking", "material": "Plywood", "description": "3/4\" plywood landing surfaces", "thickness": "3/4\"", "width": "{{landing_size}}\"", "depth": "{{landing_size}}\"", "quantity": 2, "notes": "Intermediate and top landing platforms", "sheets_needed": "{{ceil((landing_size * landing_size * 2) / (48 * 96))}}"}, {"name": "First Flight Treads", "material": "Hardwood", "description": "Tread boards for first flight", "thickness": "{{tread_thickness}}", "width": "{{stair_width}}\"", "length": "{{tread_depth + (has_nosing ? nosing_depth : 0)}}\"", "quantity": "{{first_flight_steps - 1}}", "notes": "{{has_nosing ? 'Include nosing profile' : 'Square edge'}}", "board_feet": "{{parse_thickness(tread_thickness) * stair_width * (tread_depth + (has_nosing ? nosing_depth : 0)) * (first_flight_steps - 1) / 144}}"}, {"name": "Second Flight Treads", "material": "Hardwood", "description": "Tread boards for second flight", "thickness": "{{tread_thickness}}", "width": "{{stair_width}}\"", "length": "{{tread_depth + (has_nosing ? nosing_depth : 0)}}\"", "quantity": "{{second_flight_steps - 1}}", "notes": "{{has_nosing ? 'Include nosing profile' : 'Square edge'}}", "board_feet": "{{parse_thickness(tread_thickness) * stair_width * (tread_depth + (has_nosing ? nosing_depth : 0)) * (second_flight_steps - 1) / 144}}"}, {"name": "Third Flight Treads", "material": "Hardwood", "description": "Tread boards for third flight", "thickness": "{{tread_thickness}}", "width": "{{stair_width}}\"", "length": "{{tread_depth + (has_nosing ? nosing_depth : 0)}}\"", "quantity": "{{third_flight_steps - 1}}", "notes": "{{has_nosing ? 'Include nosing profile' : 'Square edge'}}", "board_feet": "{{parse_thickness(tread_thickness) * stair_width * (tread_depth + (has_nosing ? nosing_depth : 0)) * (third_flight_steps - 1) / 144}}"}], "building_codes": {"max_riser_height": 7.75, "min_riser_height": 7.0, "max_tread_depth": 11.0, "min_tread_depth": 10.0, "max_riser_variation": 0.375, "two_r_plus_t_min": 24.0, "two_r_plus_t_max": 25.0, "min_headroom": 80.0, "min_stair_width": 36.0, "min_landing_size": 30.0, "min_well_width": 36.0}, "validation_rules": [{"rule": "optimal_riser_height >= building_codes.min_riser_height && optimal_riser_height <= building_codes.max_riser_height", "message": "Riser height must be between 7\" and 7.75\""}, {"rule": "tread_depth >= building_codes.min_tread_depth && tread_depth <= building_codes.max_tread_depth", "message": "Tread depth must be between 10\" and 11\""}, {"rule": "landing_size >= building_codes.min_landing_size", "message": "Landing size must be at least 30\""}, {"rule": "landing_size >= stair_width", "message": "Landing must be at least as wide as the stair"}, {"rule": "well_width >= building_codes.min_well_width", "message": "Stair well width must be at least 36\""}, {"rule": "well_length >= (first_flight_run + second_flight_run + landing_size)", "message": "Stair well length insufficient for stair configuration"}], "output_settings": {"dxf": {"separate_flights": true, "include_landing_frames": true, "include_well_opening": true, "layers": {"first_flight": "FIRST_FLIGHT", "second_flight": "SECOND_FLIGHT", "third_flight": "THIRD_FLIGHT", "intermediate_landing": "INT_LANDING", "top_landing": "TOP_LANDING", "well_opening": "WELL_OPENING", "dimensions": "DIMENSIONS"}}, "pdf": {"layout": "multi_view", "views": ["plan", "section", "all_flight_elevations"], "include_well_dimensions": true, "include_landing_details": true}}}