"""
Template Manager <PERSON><PERSON><PERSON>
Handles loading, validation, and processing of stair configuration templates
"""

import json
import os
import re
from typing import Dict, List, Any, Optional
from fractions import Fraction


class TemplateManager:
    """Manages stair configuration templates"""
    
    def __init__(self, template_dir: str = "templates"):
        self.template_dir = template_dir
        self.templates = {}
        self.load_all_templates()
        
    def load_all_templates(self):
        """Load all template files from the templates directory"""
        if not os.path.exists(self.template_dir):
            print(f"Warning: Template directory '{self.template_dir}' not found")
            return
            
        for filename in os.listdir(self.template_dir):
            if filename.endswith('.json'):
                template_name = filename[:-5]  # Remove .json extension
                try:
                    template_path = os.path.join(self.template_dir, filename)
                    with open(template_path, 'r') as f:
                        template_data = json.load(f)
                    
                    # Validate template structure
                    if self.validate_template(template_data):
                        self.templates[template_name] = template_data
                        print(f"✓ Loaded template: {template_name}")
                    else:
                        print(f"❌ Invalid template: {template_name}")
                        
                except Exception as e:
                    print(f"Error loading template {filename}: {e}")
                    
    def validate_template(self, template: Dict) -> bool:
        """Validate template structure"""
        required_fields = ['name', 'type', 'parameters']
        
        for field in required_fields:
            if field not in template:
                return False
                
        # Validate parameters structure
        if not isinstance(template['parameters'], dict):
            return False
            
        return True
        
    def get_template(self, template_name: str) -> Optional[Dict]:
        """Get a specific template by name"""
        return self.templates.get(template_name)
        
    def get_template_list(self) -> List[Dict]:
        """Get list of available templates with basic info"""
        template_list = []
        for name, template in self.templates.items():
            template_list.append({
                'name': name,
                'display_name': template.get('name', name),
                'description': template.get('description', ''),
                'type': template.get('type', 'unknown'),
                'category': template.get('category', 'general')
            })
        return template_list
        
    def get_stair_templates(self) -> List[Dict]:
        """Get only stair-related templates"""
        stair_templates = []
        for name, template in self.templates.items():
            if template.get('category') == 'stairs' or 'stair' in template.get('type', '').lower():
                stair_templates.append({
                    'name': name,
                    'display_name': template.get('name', name),
                    'description': template.get('description', ''),
                    'type': template.get('type', 'unknown')
                })
        return stair_templates
        
    def process_template_parameters(self, template_name: str, user_params: Dict) -> Dict:
        """Process user parameters against template and return calculated values"""
        template = self.get_template(template_name)
        if not template:
            raise ValueError(f"Template '{template_name}' not found")
            
        # Start with template defaults
        processed_params = {}
        template_params = template.get('parameters', {})
        
        # Apply defaults
        for param_name, param_def in template_params.items():
            processed_params[param_name] = param_def.get('default')
            
        # Override with user values
        for param_name, value in user_params.items():
            if param_name in template_params:
                param_def = template_params[param_name]
                processed_params[param_name] = self._convert_parameter_value(value, param_def)
                
        # Process calculations if defined
        if 'calculations' in template:
            calculated_values = self._process_calculations(template['calculations'], processed_params)
            processed_params.update(calculated_values)
            
        return processed_params
        
    def _convert_parameter_value(self, value: Any, param_def: Dict) -> Any:
        """Convert parameter value to correct type"""
        param_type = param_def.get('type', 'string')
        
        if param_type == 'float':
            if isinstance(value, str):
                # Handle fractional inputs like "1-1/4"
                return self._parse_fraction(value)
            return float(value)
        elif param_type == 'integer':
            return int(value)
        elif param_type == 'boolean':
            if isinstance(value, str):
                return value.lower() in ['true', '1', 'yes', 'on']
            return bool(value)
        else:
            return str(value)
            
    def _parse_fraction(self, fraction_str: str) -> float:
        """Parse fraction string like '1-1/4' to decimal"""
        try:
            if '-' in fraction_str:
                # Handle mixed numbers like "1-1/4"
                parts = fraction_str.split('-')
                whole = int(parts[0])
                frac = Fraction(parts[1])
                return whole + float(frac)
            elif '/' in fraction_str:
                # Handle simple fractions like "3/4"
                return float(Fraction(fraction_str))
            else:
                # Handle simple decimals or integers
                return float(fraction_str.replace('"', ''))
        except:
            return 1.0  # Default fallback
            
    def _process_calculations(self, calculations: Dict, params: Dict) -> Dict:
        """Process template calculations in dependency order"""
        calculated = {}
        all_params = params.copy()

        # Process calculations multiple times to resolve dependencies
        max_iterations = 5
        for iteration in range(max_iterations):
            progress_made = False

            for calc_name, expression in calculations.items():
                if calc_name in calculated:
                    continue  # Already calculated

                try:
                    # Replace parameter references
                    processed_expr = expression
                    for param_name, param_value in all_params.items():
                        if isinstance(param_value, (int, float)):
                            processed_expr = processed_expr.replace(param_name, str(param_value))
                        elif isinstance(param_value, bool):
                            processed_expr = processed_expr.replace(param_name, str(param_value))
                        else:
                            processed_expr = processed_expr.replace(param_name, f'"{param_value}"')

                    # Also replace already calculated values
                    for calc_param, calc_value in calculated.items():
                        processed_expr = processed_expr.replace(calc_param, str(calc_value))

                    # Try to evaluate
                    result = self._evaluate_expression(processed_expr)
                    calculated[calc_name] = result
                    all_params[calc_name] = result  # Add to available parameters
                    progress_made = True

                except Exception as e:
                    # Skip this calculation for now, might depend on others
                    continue

            # If no progress was made, break to avoid infinite loop
            if not progress_made:
                break

        # Fill in any remaining calculations with defaults
        for calc_name, expression in calculations.items():
            if calc_name not in calculated:
                print(f"Warning: Could not calculate {calc_name}, using default")
                calculated[calc_name] = 0

        return calculated
        
    def _evaluate_expression(self, expression: str) -> float:
        """Safely evaluate mathematical expressions"""
        try:
            import math

            # Create safe evaluation context
            safe_dict = {
                "__builtins__": {},
                "math": math,
                "round": round,
                "int": int,
                "float": float,
                "abs": abs,
                "min": min,
                "max": max
            }

            # Handle ternary operators (simplified)
            if '?' in expression and ':' in expression:
                # Simple ternary: condition ? true_val : false_val
                parts = expression.split('?')
                if len(parts) == 2:
                    condition = parts[0].strip()
                    values = parts[1].split(':')
                    if len(values) == 2:
                        true_val = values[0].strip()
                        false_val = values[1].strip()

                        # Evaluate condition
                        cond_result = eval(condition, safe_dict)
                        if cond_result:
                            # Handle parentheses in true_val
                            if '(' in true_val and ')' in true_val:
                                return float(eval(true_val, safe_dict))
                            else:
                                return float(true_val)
                        else:
                            # Handle parentheses in false_val
                            if '(' in false_val and ')' in false_val:
                                return float(eval(false_val, safe_dict))
                            else:
                                return float(false_val)

            # Replace common functions
            expression = expression.replace('sqrt', 'math.sqrt')
            expression = expression.replace('ceil', 'math.ceil')
            expression = expression.replace('^', '**')  # Power operator

            # Evaluate (be careful with eval in production!)
            result = eval(expression, safe_dict)
            return float(result)
        except Exception as e:
            print(f"Error evaluating expression '{expression}': {e}")
            # Return reasonable defaults for common stair calculations
            if 'optimal_riser_height' in expression:
                return 7.5  # Default riser height
            elif 'tread_depth' in expression:
                return 10.5  # Default tread depth
            elif 'stringer_count' in expression:
                return 3  # Default stringer count
            return 0.0
            
    def generate_cut_list(self, template_name: str, processed_params: Dict) -> List[Dict]:
        """Generate cut list from template and parameters"""
        template = self.get_template(template_name)
        if not template or 'cut_list' not in template:
            return []
            
        cut_list = []
        template_cut_list = template['cut_list']
        
        for item_template in template_cut_list:
            cut_item = {}
            
            # Process each field in the cut list item
            for field, value in item_template.items():
                if isinstance(value, str) and '{{' in value:
                    # Process template variables
                    processed_value = self._process_template_string(value, processed_params)
                    cut_item[field] = processed_value
                else:
                    cut_item[field] = value
                    
            cut_list.append(cut_item)
            
        return cut_list
        
    def _process_template_string(self, template_str: str, params: Dict) -> str:
        """Process template string with parameter substitution"""
        # Simple template variable replacement
        result = template_str

        # Find all {{variable}} patterns
        pattern = r'\{\{([^}]+)\}\}'
        matches = re.findall(pattern, template_str)

        for match in matches:
            variable = match.strip()

            # Handle simple variable lookup
            if variable in params:
                value = params[variable]
                result = result.replace(f'{{{{{variable}}}}}', str(value))
            else:
                # Handle expressions (simplified)
                try:
                    processed_expr = variable
                    for param_name, param_value in params.items():
                        if isinstance(param_value, (int, float)):
                            processed_expr = processed_expr.replace(param_name, str(param_value))
                        elif isinstance(param_value, bool):
                            processed_expr = processed_expr.replace(param_name, str(param_value))
                        else:
                            processed_expr = processed_expr.replace(param_name, f'"{param_value}"')

                    # Handle ternary operators for strings
                    if '?' in processed_expr and ':' in processed_expr:
                        evaluated = self._evaluate_ternary_string(processed_expr)
                    else:
                        evaluated = self._evaluate_expression(processed_expr)

                    result = result.replace(f'{{{{{variable}}}}}', str(evaluated))
                except Exception as e:
                    print(f"Error processing template string '{variable}': {e}")
                    result = result.replace(f'{{{{{variable}}}}}', '0')

        return result

    def _evaluate_ternary_string(self, expression: str) -> str:
        """Evaluate ternary expressions that might return strings"""
        try:
            import math

            safe_dict = {
                "__builtins__": {},
                "math": math,
                "round": round,
                "int": int,
                "float": float,
                "abs": abs,
                "min": min,
                "max": max
            }

            # Simple ternary: condition ? true_val : false_val
            parts = expression.split('?')
            if len(parts) == 2:
                condition = parts[0].strip()
                values = parts[1].split(':')
                if len(values) == 2:
                    true_val = values[0].strip()
                    false_val = values[1].strip()

                    # Evaluate condition
                    cond_result = eval(condition, safe_dict)
                    if cond_result:
                        # Try to evaluate as expression first, then as string
                        try:
                            return str(eval(true_val, safe_dict))
                        except:
                            return true_val.strip("'\"")  # Remove quotes if present
                    else:
                        try:
                            return str(eval(false_val, safe_dict))
                        except:
                            return false_val.strip("'\"")  # Remove quotes if present

            return expression
        except Exception as e:
            print(f"Error evaluating ternary string '{expression}': {e}")
            return "0"
        
    def validate_parameters(self, template_name: str, params: Dict) -> List[str]:
        """Validate parameters against template rules"""
        template = self.get_template(template_name)
        if not template:
            return [f"Template '{template_name}' not found"]

        errors = []

        # Check validation rules if defined
        if 'validation_rules' in template:
            try:
                processed_params = self.process_template_parameters(template_name, params)

                # Debug: print calculated values (comment out for production)
                # print(f"Debug - Calculated parameters for {template_name}:")
                # for key, value in processed_params.items():
                #     if key in ['optimal_riser_height', 'tread_depth', 'num_treads', 'num_risers']:
                #         print(f"  {key}: {value}")

                for rule in template['validation_rules']:
                    rule_expr = rule.get('rule', '')
                    message = rule.get('message', 'Validation failed')

                    try:
                        # Process rule expression
                        processed_rule = rule_expr
                        for param_name, param_value in processed_params.items():
                            if isinstance(param_value, (int, float)):
                                processed_rule = processed_rule.replace(param_name, str(param_value))

                        # print(f"Debug - Evaluating rule: {processed_rule}")

                        # Evaluate rule (simplified boolean evaluation)
                        if not self._evaluate_boolean_expression(processed_rule):
                            errors.append(message)

                    except Exception as e:
                        print(f"Rule validation error for '{rule_expr}': {e}")
                        # Don't add validation errors for calculation issues
                        # errors.append(f"Rule validation error: {e}")

            except Exception as e:
                print(f"Parameter processing error: {e}")
                # Don't fail validation if parameter processing fails

        return errors
        
    def _evaluate_boolean_expression(self, expression: str) -> bool:
        """Evaluate boolean expressions for validation"""
        try:
            # Replace operators
            expression = expression.replace('&&', ' and ')
            expression = expression.replace('||', ' or ')

            # print(f"Debug - Boolean expression: {expression}")

            # Evaluate
            result = eval(expression, {"__builtins__": {}})
            # print(f"Debug - Boolean result: {result}")
            return bool(result)
        except Exception as e:
            # print(f"Debug - Boolean evaluation error: {e}")
            return True  # Default to valid if evaluation fails
            
    def get_template_parameters_ui_config(self, template_name: str) -> Dict:
        """Get UI configuration for template parameters"""
        template = self.get_template(template_name)
        if not template:
            return {}
            
        ui_config = {
            'parameters': [],
            'groups': {}
        }
        
        template_params = template.get('parameters', {})
        
        for param_name, param_def in template_params.items():
            param_ui = {
                'name': param_name,
                'type': param_def.get('type', 'string'),
                'label': param_def.get('description', param_name),
                'default': param_def.get('default'),
                'min': param_def.get('min'),
                'max': param_def.get('max'),
                'step': param_def.get('step', 1),
                'unit': param_def.get('unit', ''),
                'options': param_def.get('options', []),
                'depends_on': param_def.get('depends_on')
            }
            
            ui_config['parameters'].append(param_ui)
            
        return ui_config
