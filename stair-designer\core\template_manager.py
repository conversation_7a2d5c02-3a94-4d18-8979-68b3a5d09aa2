"""
Template Manager <PERSON><PERSON><PERSON>
Handles loading, validation, and processing of stair configuration templates
"""

import json
import os
import re
from typing import Dict, List, Any, Optional
from fractions import Fraction


class TemplateManager:
    """Manages stair configuration templates"""
    
    def __init__(self, template_dir: str = "templates"):
        self.template_dir = template_dir
        self.templates = {}
        self.load_all_templates()
        
    def load_all_templates(self):
        """Load all template files from the templates directory"""
        if not os.path.exists(self.template_dir):
            print(f"Warning: Template directory '{self.template_dir}' not found")
            return
            
        for filename in os.listdir(self.template_dir):
            if filename.endswith('.json'):
                template_name = filename[:-5]  # Remove .json extension
                try:
                    template_path = os.path.join(self.template_dir, filename)
                    with open(template_path, 'r') as f:
                        template_data = json.load(f)
                    
                    # Validate template structure
                    if self.validate_template(template_data):
                        self.templates[template_name] = template_data
                        print(f"✓ Loaded template: {template_name}")
                    else:
                        print(f"❌ Invalid template: {template_name}")
                        
                except Exception as e:
                    print(f"Error loading template {filename}: {e}")
                    
    def validate_template(self, template: Dict) -> bool:
        """Validate template structure"""
        required_fields = ['name', 'type', 'parameters']
        
        for field in required_fields:
            if field not in template:
                return False
                
        # Validate parameters structure
        if not isinstance(template['parameters'], dict):
            return False
            
        return True
        
    def get_template(self, template_name: str) -> Optional[Dict]:
        """Get a specific template by name"""
        return self.templates.get(template_name)
        
    def get_template_list(self) -> List[Dict]:
        """Get list of available templates with basic info"""
        template_list = []
        for name, template in self.templates.items():
            template_list.append({
                'name': name,
                'display_name': template.get('name', name),
                'description': template.get('description', ''),
                'type': template.get('type', 'unknown'),
                'category': template.get('category', 'general')
            })
        return template_list
        
    def get_stair_templates(self) -> List[Dict]:
        """Get only stair-related templates"""
        stair_templates = []
        for name, template in self.templates.items():
            if template.get('category') == 'stairs' or 'stair' in template.get('type', '').lower():
                stair_templates.append({
                    'name': name,
                    'display_name': template.get('name', name),
                    'description': template.get('description', ''),
                    'type': template.get('type', 'unknown')
                })
        return stair_templates
        
    def process_template_parameters(self, template_name: str, user_params: Dict) -> Dict:
        """Process user parameters against template and return calculated values"""
        template = self.get_template(template_name)
        if not template:
            raise ValueError(f"Template '{template_name}' not found")
            
        # Start with template defaults
        processed_params = {}
        template_params = template.get('parameters', {})
        
        # Apply defaults
        for param_name, param_def in template_params.items():
            processed_params[param_name] = param_def.get('default')
            
        # Override with user values
        for param_name, value in user_params.items():
            if param_name in template_params:
                param_def = template_params[param_name]
                processed_params[param_name] = self._convert_parameter_value(value, param_def)
                
        # Process calculations if defined
        if 'calculations' in template:
            calculated_values = self._process_calculations(template['calculations'], processed_params)
            processed_params.update(calculated_values)
            
        return processed_params
        
    def _convert_parameter_value(self, value: Any, param_def: Dict) -> Any:
        """Convert parameter value to correct type"""
        param_type = param_def.get('type', 'string')
        
        if param_type == 'float':
            if isinstance(value, str):
                # Handle fractional inputs like "1-1/4"
                return self._parse_fraction(value)
            return float(value)
        elif param_type == 'integer':
            return int(value)
        elif param_type == 'boolean':
            if isinstance(value, str):
                return value.lower() in ['true', '1', 'yes', 'on']
            return bool(value)
        else:
            return str(value)
            
    def _parse_fraction(self, fraction_str: str) -> float:
        """Parse fraction string like '1-1/4' to decimal"""
        try:
            if '-' in fraction_str:
                # Handle mixed numbers like "1-1/4"
                parts = fraction_str.split('-')
                whole = int(parts[0])
                frac = Fraction(parts[1])
                return whole + float(frac)
            elif '/' in fraction_str:
                # Handle simple fractions like "3/4"
                return float(Fraction(fraction_str))
            else:
                # Handle simple decimals or integers
                return float(fraction_str.replace('"', ''))
        except:
            return 1.0  # Default fallback
            
    def _process_calculations(self, calculations: Dict, params: Dict) -> Dict:
        """Process template calculations"""
        calculated = {}
        
        # Simple expression evaluator for basic math
        for calc_name, expression in calculations.items():
            try:
                # Replace parameter references
                processed_expr = expression
                for param_name, param_value in params.items():
                    processed_expr = processed_expr.replace(param_name, str(param_value))
                    
                # Basic math operations (simplified)
                result = self._evaluate_expression(processed_expr)
                calculated[calc_name] = result
                
            except Exception as e:
                print(f"Error calculating {calc_name}: {e}")
                calculated[calc_name] = 0
                
        return calculated
        
    def _evaluate_expression(self, expression: str) -> float:
        """Safely evaluate mathematical expressions"""
        # This is a simplified evaluator - in production, use a proper math parser
        try:
            # Replace common functions
            expression = expression.replace('sqrt', 'math.sqrt')
            expression = expression.replace('ceil', 'math.ceil')
            expression = expression.replace('round', 'round')
            expression = expression.replace('^', '**')  # Power operator
            
            # Import math for functions
            import math
            
            # Evaluate (be careful with eval in production!)
            result = eval(expression, {"__builtins__": {}, "math": math})
            return float(result)
        except:
            return 0.0
            
    def generate_cut_list(self, template_name: str, processed_params: Dict) -> List[Dict]:
        """Generate cut list from template and parameters"""
        template = self.get_template(template_name)
        if not template or 'cut_list' not in template:
            return []
            
        cut_list = []
        template_cut_list = template['cut_list']
        
        for item_template in template_cut_list:
            cut_item = {}
            
            # Process each field in the cut list item
            for field, value in item_template.items():
                if isinstance(value, str) and '{{' in value:
                    # Process template variables
                    processed_value = self._process_template_string(value, processed_params)
                    cut_item[field] = processed_value
                else:
                    cut_item[field] = value
                    
            cut_list.append(cut_item)
            
        return cut_list
        
    def _process_template_string(self, template_str: str, params: Dict) -> str:
        """Process template string with parameter substitution"""
        # Simple template variable replacement
        result = template_str
        
        # Find all {{variable}} patterns
        pattern = r'\{\{([^}]+)\}\}'
        matches = re.findall(pattern, template_str)
        
        for match in matches:
            variable = match.strip()
            
            # Handle simple variable lookup
            if variable in params:
                value = params[variable]
                result = result.replace(f'{{{{{variable}}}}}', str(value))
            else:
                # Handle expressions (simplified)
                try:
                    processed_expr = variable
                    for param_name, param_value in params.items():
                        processed_expr = processed_expr.replace(param_name, str(param_value))
                    
                    evaluated = self._evaluate_expression(processed_expr)
                    result = result.replace(f'{{{{{variable}}}}}', str(evaluated))
                except:
                    result = result.replace(f'{{{{{variable}}}}}', '0')
                    
        return result
        
    def validate_parameters(self, template_name: str, params: Dict) -> List[str]:
        """Validate parameters against template rules"""
        template = self.get_template(template_name)
        if not template:
            return [f"Template '{template_name}' not found"]
            
        errors = []
        
        # Check validation rules if defined
        if 'validation_rules' in template:
            processed_params = self.process_template_parameters(template_name, params)
            
            for rule in template['validation_rules']:
                rule_expr = rule.get('rule', '')
                message = rule.get('message', 'Validation failed')
                
                try:
                    # Process rule expression
                    processed_rule = rule_expr
                    for param_name, param_value in processed_params.items():
                        processed_rule = processed_rule.replace(param_name, str(param_value))
                        
                    # Evaluate rule (simplified boolean evaluation)
                    if not self._evaluate_boolean_expression(processed_rule):
                        errors.append(message)
                        
                except Exception as e:
                    errors.append(f"Rule validation error: {e}")
                    
        return errors
        
    def _evaluate_boolean_expression(self, expression: str) -> bool:
        """Evaluate boolean expressions for validation"""
        try:
            # Replace operators
            expression = expression.replace('&&', ' and ')
            expression = expression.replace('||', ' or ')
            
            # Evaluate
            result = eval(expression, {"__builtins__": {}})
            return bool(result)
        except:
            return True  # Default to valid if evaluation fails
            
    def get_template_parameters_ui_config(self, template_name: str) -> Dict:
        """Get UI configuration for template parameters"""
        template = self.get_template(template_name)
        if not template:
            return {}
            
        ui_config = {
            'parameters': [],
            'groups': {}
        }
        
        template_params = template.get('parameters', {})
        
        for param_name, param_def in template_params.items():
            param_ui = {
                'name': param_name,
                'type': param_def.get('type', 'string'),
                'label': param_def.get('description', param_name),
                'default': param_def.get('default'),
                'min': param_def.get('min'),
                'max': param_def.get('max'),
                'step': param_def.get('step', 1),
                'unit': param_def.get('unit', ''),
                'options': param_def.get('options', []),
                'depends_on': param_def.get('depends_on')
            }
            
            ui_config['parameters'].append(param_ui)
            
        return ui_config
