{"name": "Straight Stair", "description": "Standard straight stair configuration with stringers and treads", "type": "straight_stair", "category": "stairs", "parameters": {"total_rise": {"type": "float", "default": 108.0, "min": 60.0, "max": 144.0, "unit": "inches", "description": "Total vertical rise of the stair"}, "stair_width": {"type": "float", "default": 36.0, "min": 24.0, "max": 60.0, "unit": "inches", "description": "Width of the stair"}, "tread_thickness": {"type": "string", "default": "1\"", "options": ["3/4\"", "1\"", "1-1/4\"", "1-1/2\"", "2\""], "description": "Thickness of tread material"}, "material": {"type": "string", "default": "LSL", "options": ["LSL", "LVL", "Solid Wood", "Plywood"], "description": "Stringer material type"}, "has_nosing": {"type": "boolean", "default": true, "description": "Include nosing on treads"}, "riser_type": {"type": "string", "default": "Open", "options": ["Open", "Closed", "Mixed"], "description": "Type of riser configuration"}}, "calculations": {"initial_num_risers": "round(total_rise / 7.5)", "optimal_riser_height": "total_rise / initial_num_risers", "adjusted_riser_height": "max(7.0, min(7.75, optimal_riser_height))", "final_num_risers": "round(total_rise / adjusted_riser_height)", "final_riser_height": "total_rise / final_num_risers", "num_risers": "final_num_risers", "num_treads": "num_risers - 1", "calculated_tread_depth": "25 - (2 * final_riser_height)", "tread_depth": "max(10.0, min(11.0, calculated_tread_depth))", "tread_depth_with_nosing": "tread_depth + 1", "total_run": "tread_depth * num_treads", "stringer_length": "sqrt(total_run * total_run + total_rise * total_rise)", "stringer_count": "stair_width <= 36 ? 3 : ceil(stair_width / 16)"}, "geometry": {"stringers": {"type": "array", "count": "{{stringer_count}}", "spacing": "{{stair_width / (stringer_count - 1)}}", "element": {"type": "stringer_profile", "material": "{{material}}", "width": 11.25, "thickness": 1.5, "length": "{{stringer_length}}", "profile_type": "sawtooth"}}, "treads": {"type": "array", "count": "{{num_treads}}", "element": {"type": "rectangle", "width": "{{stair_width}}", "depth": "{{tread_depth_with_nosing}}", "thickness": "{{tread_thickness}}", "nosing": "{{has_nosing}}"}}}, "cut_list": [{"name": "Stringers", "material": "{{material}}", "description": "2x12 stringer material", "thickness": "1-1/2\"", "width": "11-1/4\"", "length": "{{ceil(stringer_length / 12) * 12}}'", "quantity": "{{stringer_count}}", "notes": "Cut to stair profile using DXF template"}, {"name": "Treads", "material": "Hardwood", "description": "Tread boards", "thickness": "{{tread_thickness}}", "width": "{{stair_width}}\"", "length": "{{tread_depth_with_nosing}}\"", "quantity": "{{num_treads}}", "notes": "Include nosing profile"}], "building_codes": {"max_riser_height": 7.75, "min_riser_height": 7.0, "max_tread_depth": 11.0, "min_tread_depth": 10.0, "two_r_plus_t_min": 23.5, "two_r_plus_t_max": 25.5}, "validation_rules": [{"rule": "final_riser_height >= 7.0 && final_riser_height <= 7.75", "message": "Riser height must be between 7\" and 7.75\""}, {"rule": "tread_depth >= 10.0 && tread_depth <= 11.0", "message": "Tread depth must be between 10\" and 11\""}, {"rule": "(2 * final_riser_height + tread_depth) >= 23.5 && (2 * final_riser_height + tread_depth) <= 25.5", "message": "2R + T rule: must be between 23.5\" and 25.5\" (currently acceptable range)"}]}