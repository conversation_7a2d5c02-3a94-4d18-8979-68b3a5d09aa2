"""
Cost Estimator Module
Calculates material costs with waste buffer and labor markup for stair projects
"""

from typing import Dict, List, Any
import json
import os


class CostEstimator:
    """Class for calculating project costs"""
    
    def __init__(self):
        # Default material prices (per board foot or linear foot)
        self.material_prices = {
            'LSL': {
                'price_per_bf': 4.50,  # per board foot
                'unit': 'board_foot',
                'waste_factor': 1.15  # 15% waste
            },
            'LVL': {
                'price_per_bf': 5.25,
                'unit': 'board_foot',
                'waste_factor': 1.10  # 10% waste
            },
            'Solid Wood': {
                'price_per_bf': 6.00,
                'unit': 'board_foot',
                'waste_factor': 1.20  # 20% waste
            },
            'Plywood': {
                'price_per_sheet': 85.00,  # per 4x8 sheet
                'unit': 'sheet',
                'waste_factor': 1.10
            },
            'Lumber': {
                'price_per_lf': 3.25,  # per linear foot for 2x12
                'unit': 'linear_foot',
                'waste_factor': 1.10
            },
            'Hardwood': {
                'price_per_bf': 12.00,
                'unit': 'board_foot',
                'waste_factor': 1.15
            }
        }
        
        # Labor rates
        self.labor_rates = {
            'base_rate': 75.00,  # per hour
            'stair_complexity_multiplier': 1.5,
            'estimated_hours_per_step': 0.75
        }
        
        # Load custom pricing if available
        self.load_custom_pricing()
        
    def load_custom_pricing(self):
        """Load custom pricing from config file if it exists"""
        config_file = "config/pricing.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    custom_pricing = json.load(f)
                    
                if 'materials' in custom_pricing:
                    self.material_prices.update(custom_pricing['materials'])
                    
                if 'labor' in custom_pricing:
                    self.labor_rates.update(custom_pricing['labor'])
                    
            except Exception as e:
                print(f"Error loading custom pricing: {e}")
                
    def calculate_project_cost(self, stair_results: Dict, cut_list: List[Dict]) -> Dict:
        """Calculate total project cost including materials and labor"""
        if not stair_results.get('calculations_valid'):
            return {'error': 'Invalid stair calculations'}
            
        # Calculate material costs
        material_cost = self._calculate_material_cost(cut_list, stair_results)
        
        # Calculate labor cost
        labor_cost = self._calculate_labor_cost(stair_results)
        
        # Calculate totals
        subtotal = material_cost['total_with_waste'] + labor_cost['total_labor']
        
        # Add markup (typically 20-30% for profit)
        markup_percentage = 0.25  # 25%
        markup_amount = subtotal * markup_percentage
        
        total_cost = subtotal + markup_amount
        
        return {
            'material_cost': material_cost,
            'labor_cost': labor_cost,
            'subtotal': subtotal,
            'markup_percentage': markup_percentage * 100,
            'markup_amount': markup_amount,
            'total_cost': total_cost,
            'cost_per_step': total_cost / stair_results['rise_run']['num_treads'] if stair_results['rise_run']['num_treads'] > 0 else 0
        }
        
    def _calculate_material_cost(self, cut_list: List[Dict], stair_results: Dict) -> Dict:
        """Calculate material costs from cut list"""
        material_breakdown = {}
        total_material_cost = 0
        total_with_waste = 0
        
        for item in cut_list:
            material_type = self._map_material_type(item.get('material', 'Lumber'))
            quantity = item.get('quantity', 1)
            dimensions = item.get('dimensions', '')
            
            # Calculate material volume/area
            volume_bf = self._calculate_board_feet(dimensions, quantity)
            
            # Get pricing info
            pricing = self.material_prices.get(material_type, self.material_prices['Lumber'])
            
            # Calculate cost based on unit type
            if pricing['unit'] == 'board_foot':
                base_cost = volume_bf * pricing['price_per_bf']
            elif pricing['unit'] == 'linear_foot':
                # Extract length from dimensions for linear foot pricing
                length_ft = self._extract_length_feet(dimensions)
                base_cost = length_ft * quantity * pricing['price_per_lf']
            elif pricing['unit'] == 'sheet':
                # Calculate sheets needed
                sheets_needed = self._calculate_sheets_needed(dimensions, quantity)
                base_cost = sheets_needed * pricing['price_per_sheet']
            else:
                base_cost = 0
                
            # Apply waste factor
            cost_with_waste = base_cost * pricing['waste_factor']
            
            material_breakdown[item['item']] = {
                'base_cost': base_cost,
                'waste_factor': pricing['waste_factor'],
                'cost_with_waste': cost_with_waste,
                'quantity': quantity,
                'material_type': material_type,
                'board_feet': volume_bf if pricing['unit'] == 'board_foot' else None
            }
            
            total_material_cost += base_cost
            total_with_waste += cost_with_waste
            
        return {
            'breakdown': material_breakdown,
            'total_base': total_material_cost,
            'total_with_waste': total_with_waste,
            'total_waste_amount': total_with_waste - total_material_cost
        }
        
    def _calculate_labor_cost(self, stair_results: Dict) -> Dict:
        """Calculate labor costs"""
        rise_run = stair_results['rise_run']
        num_steps = rise_run['num_treads']
        
        # Base labor calculation
        base_hours = num_steps * self.labor_rates['estimated_hours_per_step']
        
        # Apply complexity multiplier
        complexity_hours = base_hours * self.labor_rates['stair_complexity_multiplier']
        
        # Add setup and finishing time
        setup_hours = 2.0  # Setup and preparation
        finishing_hours = num_steps * 0.25  # Finishing per step
        
        total_hours = complexity_hours + setup_hours + finishing_hours
        
        # Calculate cost
        hourly_rate = self.labor_rates['base_rate']
        total_labor_cost = total_hours * hourly_rate
        
        return {
            'base_hours': base_hours,
            'complexity_hours': complexity_hours,
            'setup_hours': setup_hours,
            'finishing_hours': finishing_hours,
            'total_hours': total_hours,
            'hourly_rate': hourly_rate,
            'total_labor': total_labor_cost
        }
        
    def _map_material_type(self, material_description: str) -> str:
        """Map material description to pricing category"""
        material_lower = material_description.lower()
        
        if 'lsl' in material_lower:
            return 'LSL'
        elif 'lvl' in material_lower:
            return 'LVL'
        elif 'hardwood' in material_lower or 'engineered' in material_lower:
            return 'Hardwood'
        elif 'plywood' in material_lower:
            return 'Plywood'
        elif 'lumber' in material_lower:
            return 'Lumber'
        else:
            return 'Lumber'  # Default
            
    def _calculate_board_feet(self, dimensions: str, quantity: int) -> float:
        """Calculate board feet from dimension string"""
        try:
            # Parse dimensions like "1-1/2\" x 11-1/4\" x 144\""
            # Remove quotes and split by 'x'
            dims = dimensions.replace('"', '').replace("'", '').split(' x ')
            
            if len(dims) >= 3:
                thickness = self._parse_dimension(dims[0])
                width = self._parse_dimension(dims[1])
                length = self._parse_dimension(dims[2])
                
                # Convert to board feet (thickness * width * length / 144)
                board_feet = (thickness * width * length) / 144
                return board_feet * quantity
            else:
                return 0
        except:
            return 0
            
    def _parse_dimension(self, dim_str: str) -> float:
        """Parse dimension string like '1-1/2' to decimal"""
        try:
            if '-' in dim_str:
                parts = dim_str.split('-')
                whole = float(parts[0])
                frac_parts = parts[1].split('/')
                fraction = float(frac_parts[0]) / float(frac_parts[1])
                return whole + fraction
            elif '/' in dim_str:
                frac_parts = dim_str.split('/')
                return float(frac_parts[0]) / float(frac_parts[1])
            else:
                return float(dim_str)
        except:
            return 0
            
    def _extract_length_feet(self, dimensions: str) -> float:
        """Extract length in feet from dimensions string"""
        try:
            dims = dimensions.replace('"', '').replace("'", '').split(' x ')
            if len(dims) >= 3:
                length_inches = self._parse_dimension(dims[2])
                return length_inches / 12  # Convert to feet
            return 0
        except:
            return 0
            
    def _calculate_sheets_needed(self, dimensions: str, quantity: int) -> int:
        """Calculate number of 4x8 sheets needed"""
        # For plywood, calculate based on area
        # This is a simplified calculation
        return max(1, quantity)  # Minimum 1 sheet
        
    def format_currency(self, amount: float) -> str:
        """Format amount as currency"""
        return f"${amount:,.2f}"
        
    def generate_cost_report(self, cost_data: Dict) -> str:
        """Generate formatted cost report"""
        if 'error' in cost_data:
            return f"Error: {cost_data['error']}"
            
        report = []
        report.append("COST ESTIMATE")
        report.append("=" * 50)
        report.append("")
        
        # Material costs
        report.append("MATERIAL COSTS:")
        report.append("-" * 30)
        
        material_cost = cost_data['material_cost']
        for item, details in material_cost['breakdown'].items():
            report.append(f"{item}:")
            report.append(f"  Base Cost: {self.format_currency(details['base_cost'])}")
            report.append(f"  With Waste ({details['waste_factor']:.1%}): {self.format_currency(details['cost_with_waste'])}")
            if details['board_feet']:
                report.append(f"  Board Feet: {details['board_feet']:.1f}")
            report.append("")
            
        report.append(f"Total Material (base): {self.format_currency(material_cost['total_base'])}")
        report.append(f"Total Material (with waste): {self.format_currency(material_cost['total_with_waste'])}")
        report.append("")
        
        # Labor costs
        report.append("LABOR COSTS:")
        report.append("-" * 30)
        
        labor_cost = cost_data['labor_cost']
        report.append(f"Estimated Hours: {labor_cost['total_hours']:.1f}")
        report.append(f"Hourly Rate: {self.format_currency(labor_cost['hourly_rate'])}")
        report.append(f"Total Labor: {self.format_currency(labor_cost['total_labor'])}")
        report.append("")
        
        # Totals
        report.append("PROJECT TOTAL:")
        report.append("-" * 30)
        report.append(f"Subtotal: {self.format_currency(cost_data['subtotal'])}")
        report.append(f"Markup ({cost_data['markup_percentage']:.0f}%): {self.format_currency(cost_data['markup_amount'])}")
        report.append(f"TOTAL PROJECT COST: {self.format_currency(cost_data['total_cost'])}")
        report.append(f"Cost per Step: {self.format_currency(cost_data['cost_per_step'])}")
        
        return "\n".join(report)
