{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "U-shaped layout template for three-sided applications", "type": "u_shape", "parameters": {"outer_length": {"type": "float", "default": 1200.0, "min": 200.0, "max": 10000.0, "unit": "mm", "description": "Overall length of the U-shape"}, "outer_width": {"type": "float", "default": 800.0, "min": 200.0, "max": 10000.0, "unit": "mm", "description": "Overall width of the U-shape"}, "inner_length": {"type": "float", "default": 600.0, "min": 100.0, "max": 8000.0, "unit": "mm", "description": "Length of the inner cutout"}, "inner_width": {"type": "float", "default": 400.0, "min": 100.0, "max": 6000.0, "unit": "mm", "description": "Width of the inner cutout"}, "thickness": {"type": "float", "default": 18.0, "min": 1.0, "max": 100.0, "unit": "mm", "description": "Material thickness"}, "corner_radius": {"type": "float", "default": 25.0, "min": 0.0, "max": 100.0, "unit": "mm", "description": "Radius of inner corners"}, "stringer_spacing": {"type": "float", "default": 400.0, "min": 200.0, "max": 800.0, "unit": "mm", "description": "Spacing between stringers"}, "stringer_width": {"type": "float", "default": 50.0, "min": 20.0, "max": 200.0, "unit": "mm", "description": "Width of each stringer"}, "edge_margin": {"type": "float", "default": 25.0, "min": 10.0, "max": 100.0, "unit": "mm", "description": "Margin from edges"}}, "geometry": {"main_panel": {"type": "u_shape", "outer": {"width": "{{outer_width}}", "height": "{{outer_length}}"}, "inner": {"width": "{{inner_width}}", "height": "{{inner_length}}", "position": {"x": "{{(outer_width - inner_width) / 2}}", "y": "{{outer_length - inner_length}}"}}, "corner_radius": "{{corner_radius}}"}, "stringers_bottom": {"type": "array", "pattern": "linear", "count": "{{(outer_width - 2 * edge_margin) / stringer_spacing}}", "element": {"type": "rectangle", "width": "{{stringer_width}}", "height": "{{outer_length - inner_length - edge_margin}}", "position": {"x": "{{edge_margin + index * stringer_spacing}}", "y": "{{edge_margin}}"}}}, "stringers_left": {"type": "array", "pattern": "linear", "count": "{{(inner_length - 2 * edge_margin) / stringer_spacing}}", "element": {"type": "rectangle", "width": "{{stringer_width}}", "height": "{{(outer_width - inner_width) / 2 - edge_margin}}", "position": {"x": "{{edge_margin}}", "y": "{{outer_length - inner_length + edge_margin + index * stringer_spacing}}"}, "rotation": 90}}, "stringers_right": {"type": "array", "pattern": "linear", "count": "{{(inner_length - 2 * edge_margin) / stringer_spacing}}", "element": {"type": "rectangle", "width": "{{stringer_width}}", "height": "{{(outer_width - inner_width) / 2 - edge_margin}}", "position": {"x": "{{outer_width - (outer_width - inner_width) / 2 + edge_margin}}", "y": "{{outer_length - inner_length + edge_margin + index * stringer_spacing}}"}, "rotation": 90}}}, "cut_list": [{"name": "Main U-Panel", "material": "Plywood", "thickness": "{{thickness}}", "description": "U-shaped main panel", "area": "{{outer_length * outer_width - inner_length * inner_width}}", "quantity": 1}, {"name": "Stringer - Bottom", "material": "Lumber", "thickness": "{{thickness}}", "width": "{{stringer_width}}", "length": "{{outer_length - inner_length - edge_margin}}", "quantity": "{{(outer_width - 2 * edge_margin) / stringer_spacing}}"}, {"name": "Stringer - Sides", "material": "Lumber", "thickness": "{{thickness}}", "width": "{{stringer_width}}", "length": "{{(outer_width - inner_width) / 2 - edge_margin}}", "quantity": "{{2 * (inner_length - 2 * edge_margin) / stringer_spacing}}"}]}